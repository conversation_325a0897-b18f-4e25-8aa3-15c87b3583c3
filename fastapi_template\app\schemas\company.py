# app/schemas/company.py
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from app.models.company import CompanyRole

class CompanyBase(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    description: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    website: Optional[str] = None
    siret: Optional[str] = None
    is_active: Optional[bool] = True

class CompanyCreate(CompanyBase):
    name: str
    code: str

class CompanyUpdate(CompanyBase):
    pass

class CompanyInDBBase(CompanyBase):
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Company(CompanyInDBBase):
    pass

class UserCompanyBase(BaseModel):
    user_id: int
    company_id: int
    role_name: str = Field(..., max_length=50, description="Nom du rôle dans l'entreprise")
    is_default: Optional[bool] = False
    is_active: Optional[bool] = True

class UserCompanyCreate(UserCompanyBase):
    invited_by: Optional[int] = None

class UserCompanyUpdate(BaseModel):
    role_name: Optional[str] = Field(None, max_length=50)
    is_default: Optional[bool] = None
    is_active: Optional[bool] = None

class UserCompany(UserCompanyBase):
    id: int
    invited_by: Optional[int] = None
    invited_at: Optional[datetime] = None
    joined_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    # Propriétés calculées
    role: Optional[CompanyRole] = None
    permissions: List[str] = Field(default_factory=list)

    class Config:
        from_attributes = True

class UserCompanyWithDetails(UserCompany):
    """UserCompany avec détails utilisateur et entreprise"""
    user_email: Optional[str] = None
    user_first_name: Optional[str] = None
    user_last_name: Optional[str] = None
    company_name: Optional[str] = None

class CompanySettings(BaseModel):
    id: Optional[int] = None
    company_id: int
    default_currency: Optional[str] = "EUR"
    date_format: Optional[str] = "DD/MM/YYYY"
    time_format: Optional[str] = "24h"
    language: Optional[str] = "fr"
    logo_url: Optional[str] = None

    class Config:
        from_attributes = True

# Nouveaux schémas pour la gestion RBAC
class CompanyRoleAssignment(BaseModel):
    """Attribution d'un rôle à un utilisateur dans une entreprise"""
    user_id: int
    company_id: int
    role_name: str = Field(..., description="Nom du rôle à attribuer")

class CompanyPermissionCheck(BaseModel):
    """Vérification de permission dans une entreprise"""
    user_id: int
    company_id: int
    permission: str
    has_permission: bool

class CompanyRolesList(BaseModel):
    """Liste des rôles disponibles pour une entreprise"""
    company_id: int
    available_roles: List[str] = Field(default_factory=list)
    role_descriptions: dict = Field(default_factory=dict)

class CompanyWithUsers(Company):
    """Entreprise avec ses utilisateurs et leurs rôles"""
    users: List[UserCompanyWithDetails] = Field(default_factory=list)
    total_users: int = 0
    active_users: int = 0

class CompanyRolePermissions(BaseModel):
    """Permissions d'un rôle dans une entreprise"""
    company_id: int
    role_name: str
    permissions: List[str] = Field(default_factory=list)
    permission_count: int = 0