# app/models/rbac.py
"""
Modèles <PERSON> (Role-Based Access Control) pour le système de permissions flexible
"""
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, DateTime, ForeignKey, Text, UniqueConstraint
from sqlalchemy.orm import relationship
from datetime import datetime
from app.core.database import Base

class Role(Base):
    """
    Table des rôles disponibles dans le système
    Pas de hiérarchie - juste des étiquettes
    """
    __tablename__ = "roles"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, nullable=False, index=True)
    description = Column(Text)
    is_system_role = Column(Boolean, default=False)  # True pour SUPER_ADMIN, False pour rôles métier
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships - Pas de relation directe car on utilise role_name comme string

    def __repr__(self):
        return f"<Role(name='{self.name}', is_system={self.is_system_role})>"

class Permission(Base):
    """
    Table des permissions granulaires sur les ressources
    """
    __tablename__ = "permissions"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, index=True)  # ex: "projects.create"
    resource = Column(String(50), nullable=False, index=True)  # ex: "projects"
    action = Column(String(50), nullable=False, index=True)    # ex: "create"
    description = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    company_roles = relationship("CompanyRolePermission", back_populates="permission", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Permission(name='{self.name}', resource='{self.resource}', action='{self.action}')>"

class CompanyRolePermission(Base):
    """
    Table de liaison entre rôles et permissions pour une entreprise donnée
    Permet d'avoir des permissions différentes par entreprise pour le même rôle
    """
    __tablename__ = "company_role_permissions"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id", ondelete="CASCADE"), nullable=False)
    role_name = Column(String(50), nullable=False, index=True)  # Nom du rôle (pas FK pour flexibilité)
    permission_id = Column(Integer, ForeignKey("permissions.id", ondelete="CASCADE"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Contrainte d'unicité pour éviter les doublons
    __table_args__ = (
        UniqueConstraint('company_id', 'role_name', 'permission_id', name='uq_company_role_permission'),
    )

    # Relationships
    company = relationship("Company", back_populates="role_permissions")
    permission = relationship("Permission", back_populates="company_roles")
    # Pas de relation vers Role car on utilise role_name comme string pour la flexibilité

    def __repr__(self):
        return f"<CompanyRolePermission(company_id={self.company_id}, role='{self.role_name}', permission_id={self.permission_id})>"
