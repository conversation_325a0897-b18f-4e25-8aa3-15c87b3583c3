#!/usr/bin/env python3
"""
Script pour créer <NAME_EMAIL> avec le mot de passe orbis123!
et l'associer comme admin à l'entreprise ORBIS
"""

import asyncio
import uuid
from datetime import datetime
from supabase import create_client, Client
from app.core.database import get_db
from app.models.user import User
from app.models.company import Company, UserCompany
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError

async def create_test_user():
    # Configuration Supabase
    url = 'https://ckqxfylgfcbutcwvqepp.supabase.co'
    service_key = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNrcXhmeWxnZmNidXRjd3ZxZXBwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTAyMTM5NywiZXhwIjoyMDY2NTk3Mzk3fQ.BCPC-CYoqZ8GNkOIRPw1UhzdqNuBGEx7-ckr1a05Ggc'
    
    # Créer un client admin Supabase
    supabase: Client = create_client(url, service_key)
    
    email = "<EMAIL>"
    password = "orbis123!"
    
    print(f"🚀 Création de l'utilisateur {email}...")
    
    try:
        # 1. Créer l'utilisateur dans Supabase Auth
        print("1. Création dans Supabase Auth...")
        auth_response = supabase.auth.admin.create_user({
            "email": email,
            "password": password,
            "email_confirm": True
        })
        
        supabase_user_id = auth_response.user.id
        print(f"✅ Utilisateur créé dans Supabase avec l'ID: {supabase_user_id}")
        
        # 2. Créer l'utilisateur dans la base de données locale
        async for db in get_db():
            print("2. Recherche de l'entreprise ORBIS...")
            
            # Trouver l'entreprise ORBIS
            company_result = await db.execute(
                select(Company).where(Company.name.ilike('%ORBIS%'))
            )
            company = company_result.scalar_one_or_none()
            
            if not company:
                print("❌ Entreprise ORBIS non trouvée!")
                return
            
            print(f"✅ Entreprise trouvée: {company.name} (ID: {company.id})")
            
            # Vérifier si l'utilisateur existe déjà
            existing_user_result = await db.execute(
                select(User).where(User.email == email)
            )
            existing_user = existing_user_result.scalar_one_or_none()
            
            if existing_user:
                print(f"⚠️ L'utilisateur {email} existe déjà dans la base locale")
                user = existing_user
                # Mettre à jour le supabase_user_id si nécessaire
                if user.supabase_user_id != supabase_user_id:
                    user.supabase_user_id = supabase_user_id
                    await db.commit()
                    print("✅ Supabase ID mis à jour")
            else:
                print("3. Création dans la base de données locale...")
                
                # Créer l'utilisateur local
                user = User(
                    email=email,
                    first_name="Test",
                    last_name="ORBIS",
                    role="ADMIN",  # Rôle système
                    supabase_user_id=supabase_user_id,
                    is_active=True,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                db.add(user)
                await db.commit()
                await db.refresh(user)
                print(f"✅ Utilisateur créé dans la base locale avec l'ID: {user.id}")
            
            # 4. Associer l'utilisateur à l'entreprise ORBIS comme ADMIN
            print("4. Association à l'entreprise ORBIS...")
            
            # Vérifier si l'association existe déjà
            existing_association_result = await db.execute(
                select(UserCompany).where(
                    UserCompany.user_id == user.id,
                    UserCompany.company_id == company.id
                )
            )
            existing_association = existing_association_result.scalar_one_or_none()
            
            if existing_association:
                print("⚠️ L'association utilisateur-entreprise existe déjà")
                # Mettre à jour le rôle si nécessaire
                if existing_association.role_name != "ADMIN":
                    existing_association.role_name = "ADMIN"
                    await db.commit()
                    print("✅ Rôle mis à jour vers ADMIN")
            else:
                # Créer l'association utilisateur-entreprise
                user_company = UserCompany(
                    user_id=user.id,
                    company_id=company.id,
                    role_name="ADMIN",  # Rôle business dans l'entreprise
                    created_at=datetime.utcnow()
                )
                
                db.add(user_company)
                await db.commit()
                print(f"✅ Utilisateur associé à l'entreprise {company.name} avec le rôle ADMIN")
            
            print("\n🎉 SUCCÈS ! Utilisateur créé avec succès !")
            print(f"📧 Email: {email}")
            print(f"🔑 Mot de passe: {password}")
            print(f"🏢 Entreprise: {company.name}")
            print(f"👤 Rôle: ADMIN")
            print(f"\n🌐 Vous pouvez maintenant vous connecter sur: http://localhost:3000/auth/login")
            
            break
            
    except Exception as e:
        print(f"❌ Erreur lors de la création: {e}")
        # Si l'utilisateur a été créé dans Supabase mais pas dans la base locale,
        # on peut essayer de le supprimer de Supabase
        try:
            if 'supabase_user_id' in locals():
                supabase.auth.admin.delete_user(supabase_user_id)
                print("🧹 Utilisateur supprimé de Supabase suite à l'erreur")
        except:
            pass

if __name__ == "__main__":
    asyncio.run(create_test_user())
