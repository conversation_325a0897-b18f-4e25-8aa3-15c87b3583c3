# tests/test_technical_documents.py
import pytest
import asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.main import app
from app.core.database import get_db
from app.models.document import TechnicalDocument, TechnicalDocumentCompany, DocumentType
from app.models.project import Project
from app.models.user import User
from app.models.company import Company
from app.schemas.technical_document import TechnicalDocumentCreate, TechnicalDocumentUpdate

# Fixtures pour les tests
@pytest.fixture
async def async_client():
    """Client HTTP asynchrone pour les tests"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

@pytest.fixture
async def db_session():
    """Session de base de données pour les tests"""
    async for session in get_db():
        yield session

@pytest.fixture
async def test_user(db_session: AsyncSession):
    """Utilisateur de test"""
    # Récupérer un utilisateur existant ou en créer un
    result = await db_session.execute(select(User).limit(1))
    user = result.scalar_one_or_none()
    
    if not user:
        pytest.skip("Aucun utilisateur disponible pour les tests")
    
    return user

@pytest.fixture
async def test_project(db_session: AsyncSession):
    """Projet de test"""
    # Récupérer un projet existant ou en créer un
    result = await db_session.execute(select(Project).limit(1))
    project = result.scalar_one_or_none()
    
    if not project:
        pytest.skip("Aucun projet disponible pour les tests")
    
    return project

@pytest.fixture
async def test_companies(db_session: AsyncSession):
    """Entreprises de test"""
    result = await db_session.execute(select(Company).limit(2))
    companies = result.scalars().all()
    
    if len(companies) < 1:
        pytest.skip("Pas assez d'entreprises pour les tests")
    
    return companies

@pytest.fixture
async def auth_headers(test_user: User):
    """Headers d'authentification pour les tests"""
    # Simuler un token JWT valide
    # En production, vous devriez générer un vrai token
    return {
        "Authorization": "Bearer test-token",
        "Content-Type": "application/json"
    }

class TestTechnicalDocumentsCRUD:
    """Tests CRUD pour les documents techniques"""
    
    async def test_create_cctp_document(
        self, 
        async_client: AsyncClient, 
        test_project: Project,
        test_companies: list,
        auth_headers: dict
    ):
        """Test de création d'un document CCTP"""
        document_data = {
            "name": "Test CCTP - Gros œuvre",
            "type_document": "CCTP",
            "content": "<h1>Test CCTP</h1><p>Contenu de test</p>",
            "project_id": test_project.id,
            "company_ids": [test_companies[0].id] if test_companies else []
        }
        
        response = await async_client.post(
            "/api/v1/technical-documents/",
            json=document_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == document_data["name"]
        assert data["type_document"] == "CCTP"
        assert data["project_id"] == test_project.id
        
        return data["id"]  # Retourner l'ID pour les autres tests
    
    async def test_create_dpgf_document(
        self, 
        async_client: AsyncClient, 
        test_project: Project,
        test_companies: list,
        auth_headers: dict
    ):
        """Test de création d'un document DPGF"""
        document_data = {
            "name": "Test DPGF - Maçonnerie",
            "type_document": "DPGF",
            "content": "<h1>Test DPGF</h1><table><tr><td>Test</td></tr></table>",
            "project_id": test_project.id,
            "company_ids": [test_companies[0].id] if test_companies else []
        }
        
        response = await async_client.post(
            "/api/v1/technical-documents/",
            json=document_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == document_data["name"]
        assert data["type_document"] == "DPGF"
        
        return data["id"]
    
    async def test_get_documents_list(
        self, 
        async_client: AsyncClient, 
        test_project: Project,
        auth_headers: dict
    ):
        """Test de récupération de la liste des documents"""
        response = await async_client.get(
            f"/api/v1/technical-documents/?project_id={test_project.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    async def test_get_document_by_id(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test de récupération d'un document par ID"""
        # D'abord créer un document
        document_id = await self.test_create_cctp_document(
            async_client, 
            test_project, 
            test_companies, 
            auth_headers
        )
        
        response = await async_client.get(
            f"/api/v1/technical-documents/{document_id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == document_id
        assert "content" in data
        assert "companies" in data
    
    async def test_update_document(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test de mise à jour d'un document"""
        # Créer un document d'abord
        document_id = await self.test_create_cctp_document(
            async_client, 
            test_project, 
            test_companies, 
            auth_headers
        )
        
        update_data = {
            "name": "Test CCTP - Mis à jour",
            "content": "<h1>Contenu mis à jour</h1>"
        }
        
        response = await async_client.put(
            f"/api/v1/technical-documents/{document_id}",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
    
    async def test_delete_document(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test de suppression d'un document"""
        # Créer un document d'abord
        document_id = await self.test_create_cctp_document(
            async_client, 
            test_project, 
            test_companies, 
            auth_headers
        )
        
        response = await async_client.delete(
            f"/api/v1/technical-documents/{document_id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data

class TestCompanyAssociations:
    """Tests pour les associations avec les entreprises"""
    
    async def test_add_companies_to_document(
        self, 
        async_client: AsyncClient, 
        test_companies: list,
        auth_headers: dict
    ):
        """Test d'ajout d'entreprises à un document"""
        # Créer un document d'abord
        document_id = await TestTechnicalDocumentsCRUD().test_create_cctp_document(
            async_client, 
            test_project, 
            test_companies, 
            auth_headers
        )
        
        if len(test_companies) > 1:
            company_ids = [test_companies[1].id]
            
            response = await async_client.post(
                f"/api/v1/technical-documents/{document_id}/companies",
                json=company_ids,
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert isinstance(data, list)
    
    async def test_remove_company_from_document(
        self, 
        async_client: AsyncClient, 
        test_companies: list,
        auth_headers: dict
    ):
        """Test de suppression d'une entreprise d'un document"""
        # Créer un document avec plusieurs entreprises
        document_id = await TestTechnicalDocumentsCRUD().test_create_cctp_document(
            async_client, 
            test_project, 
            test_companies, 
            auth_headers
        )
        
        if len(test_companies) > 1:
            # Ajouter une entreprise
            await self.test_add_companies_to_document(
                async_client, 
                test_companies, 
                auth_headers
            )
            
            # Puis la supprimer
            response = await async_client.delete(
                f"/api/v1/technical-documents/{document_id}/companies/{test_companies[1].id}",
                headers=auth_headers
            )
            
            assert response.status_code == 200

class TestTextEnhancement:
    """Tests pour l'amélioration de texte avec ChatGPT"""
    
    async def test_enhance_text_endpoint(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test de l'endpoint d'amélioration de texte"""
        enhancement_data = {
            "text": "Ceci est un texte de test pour l'amélioration",
            "prompt_type": "enhance",
            "document_type": "CCTP",
            "context": "Test unitaire"
        }
        
        response = await async_client.post(
            "/api/v1/technical-documents/enhance-text",
            json=enhancement_data,
            headers=auth_headers
        )
        
        # Le test peut échouer si OpenAI n'est pas configuré
        if response.status_code == 500:
            pytest.skip("OpenAI non configuré pour les tests")
        
        assert response.status_code == 200
        data = response.json()
        assert "original_text" in data
        assert "enhanced_text" in data
        assert data["prompt_type"] == "enhance"
        assert data["document_type"] == "CCTP"
    
    async def test_get_prompt_types(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test de récupération des types de prompts"""
        response = await async_client.get(
            "/api/v1/technical-documents/prompt-types/CCTP",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "document_type" in data
        assert "available_prompt_types" in data
        assert isinstance(data["available_prompt_types"], list)
    
    async def test_get_supported_document_types(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test de récupération des types de documents supportés"""
        response = await async_client.get(
            "/api/v1/technical-documents/supported-document-types",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "supported_document_types" in data
        assert "CCTP" in data["supported_document_types"]
        assert "DPGF" in data["supported_document_types"]

class TestValidation:
    """Tests de validation des données"""
    
    async def test_invalid_document_type(
        self, 
        async_client: AsyncClient, 
        test_project: Project,
        auth_headers: dict
    ):
        """Test avec un type de document invalide"""
        document_data = {
            "name": "Test document invalide",
            "type_document": "INVALID_TYPE",
            "project_id": test_project.id
        }
        
        response = await async_client.post(
            "/api/v1/technical-documents/",
            json=document_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422  # Validation error
    
    async def test_missing_required_fields(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test avec des champs obligatoires manquants"""
        document_data = {
            "type_document": "CCTP"
            # Manque name et project_id
        }
        
        response = await async_client.post(
            "/api/v1/technical-documents/",
            json=document_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422  # Validation error
    
    async def test_nonexistent_project(
        self, 
        async_client: AsyncClient, 
        auth_headers: dict
    ):
        """Test avec un projet inexistant"""
        document_data = {
            "name": "Test document",
            "type_document": "CCTP",
            "project_id": 99999  # ID inexistant
        }
        
        response = await async_client.post(
            "/api/v1/technical-documents/",
            json=document_data,
            headers=auth_headers
        )
        
        assert response.status_code == 404  # Not found

# Configuration pytest
def pytest_configure(config):
    """Configuration pytest pour les tests asynchrones"""
    config.addinivalue_line(
        "markers", "asyncio: mark test as async"
    )
