# app/api/api_v1/endpoints/public.py
from typing import Any, List
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.api import deps
from app.models.project import Project
from app.models.employee import Employee
from app.models.company import Company
from app.models.user import User
from app.schemas.project import Project as ProjectSchema
from app.schemas.employee import Employee as EmployeeSchema

router = APIRouter()

def get_db_sync():
    """Get database session for public endpoints"""
    from app.core.database import SessionLocal
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.get("/projects", response_model=List[ProjectSchema])
def read_public_projects(
    db: Session = Depends(get_db_sync),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    Retrieve projects without authentication (for demo purposes).
    """
    projects = db.query(Project).offset(skip).limit(limit).all()
    return projects

@router.get("/projects/{id}", response_model=ProjectSchema)
def read_public_project(
    *,
    db: Session = Depends(get_db_sync),
    id: int,
) -> Any:
    """
    Get project by ID without authentication (for demo purposes).
    """
    project = db.query(Project).filter(Project.id == id).first()
    if not project:
        from fastapi import HTTPException
        raise HTTPException(status_code=404, detail="Project not found")
    return project

@router.get("/employees", response_model=List[EmployeeSchema])
def read_public_employees(
    db: Session = Depends(get_db_sync),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    Retrieve employees without authentication (for demo purposes).
    """
    employees = db.query(Employee).offset(skip).limit(limit).all()
    return employees

@router.get("/employees/{id}", response_model=EmployeeSchema)
def read_public_employee(
    *,
    db: Session = Depends(get_db_sync),
    id: int,
) -> Any:
    """
    Get employee by ID without authentication (for demo purposes).
    """
    employee = db.query(Employee).filter(Employee.id == id).first()
    if not employee:
        from fastapi import HTTPException
        raise HTTPException(status_code=404, detail="Employee not found")
    return employee

@router.get("/dashboard/stats")
def get_public_dashboard_stats(
    db: Session = Depends(get_db_sync),
) -> Any:
    """
    Get dashboard statistics without authentication (for demo purposes).
    """
    # Count total projects
    total_projects = db.query(Project).count()
    
    # Count active projects
    active_projects = db.query(Project).filter(Project.status == "active").count()
    
    # Count total employees
    total_employees = db.query(Employee).count()
    
    # Count active employees
    active_employees = db.query(Employee).filter(Employee.is_active == True).count()
    
    # Count companies
    total_companies = db.query(Company).count()
    
    # Calculate some basic stats
    completed_projects = db.query(Project).filter(Project.status == "completed").count()
    on_hold_projects = db.query(Project).filter(Project.status == "on_hold").count()
    
    return {
        "total_projects": total_projects,
        "active_projects": active_projects,
        "completed_projects": completed_projects,
        "on_hold_projects": on_hold_projects,
        "total_employees": total_employees,
        "active_employees": active_employees,
        "total_companies": total_companies,
        "project_completion_rate": round((completed_projects / total_projects * 100) if total_projects > 0 else 0, 1)
    }

@router.get("/documents")
def get_public_documents(
    db: Session = Depends(get_db_sync),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    Get documents without authentication (for demo purposes).
    """
    from app.models.document import Document
    documents = db.query(Document).offset(skip).limit(limit).all()
    
    # Convert to dict format for JSON response
    document_list = []
    for doc in documents:
        document_list.append({
            "id": doc.id,
            "name": doc.name,
            "file_path": doc.file_path,
            "file_type": doc.file_type,
            "file_size": doc.file_size,
            "project_id": doc.project_id,
            "uploaded_by": doc.uploaded_by,
            "created_at": doc.created_at.isoformat() if doc.created_at else None,
            "updated_at": doc.updated_at.isoformat() if doc.updated_at else None
        })
    
    return document_list