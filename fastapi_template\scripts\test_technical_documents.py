#!/usr/bin/env python3
"""
Script de test pour valider le module des documents techniques
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import text, select
from app.core.database import get_async_engine
from app.models.document import TechnicalDocument, TechnicalDocumentCompany, DocumentType
from app.models.project import Project
from app.models.user import User
from app.models.company import Company

async def test_database_structure():
    """Tester la structure de la base de données"""
    print("🔍 Test de la structure de la base de données...")
    
    engine = get_async_engine()
    
    tests = [
        ("Table technical_documents", "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'technical_documents'"),
        ("Table technical_document_companies", "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'technical_document_companies'"),
        ("Enum DocumentType", "SELECT COUNT(*) FROM information_schema.types WHERE typname = 'documenttype'"),
        ("Index sur type_document", "SELECT COUNT(*) FROM pg_indexes WHERE tablename = 'technical_documents' AND indexname = 'idx_technical_documents_type'"),
        ("Contrainte FK project_id", "SELECT COUNT(*) FROM information_schema.table_constraints WHERE table_name = 'technical_documents' AND constraint_type = 'FOREIGN KEY'"),
    ]
    
    try:
        async with engine.begin() as conn:
            for test_name, query in tests:
                result = await conn.execute(text(query))
                count = result.scalar()
                status = "✅" if count > 0 else "❌"
                print(f"  {status} {test_name}: {count}")
                
    except Exception as e:
        print(f"❌ Erreur lors du test de structure: {e}")
        return False
    finally:
        await engine.dispose()
    
    return True

async def test_crud_operations():
    """Tester les opérations CRUD de base"""
    print("\n🔍 Test des opérations CRUD...")
    
    engine = get_async_engine()
    
    try:
        async with engine.begin() as conn:
            # Vérifier qu'il y a des données de base
            projects = await conn.execute(text("SELECT id, name FROM projects LIMIT 1"))
            project = projects.fetchone()
            
            users = await conn.execute(text("SELECT id, email FROM users LIMIT 1"))
            user = users.fetchone()
            
            companies = await conn.execute(text("SELECT id, name FROM companies LIMIT 2"))
            company_list = companies.fetchall()
            
            if not project or not user or len(company_list) == 0:
                print("❌ Pas assez de données de base pour les tests")
                print(f"   Projets: {1 if project else 0}")
                print(f"   Utilisateurs: {1 if user else 0}")
                print(f"   Entreprises: {len(company_list)}")
                return False
            
            print(f"✅ Données de base disponibles:")
            print(f"   Projet: {project.name}")
            print(f"   Utilisateur: {user.email}")
            print(f"   Entreprises: {len(company_list)}")
            
            # Test 1: Créer un document CCTP
            print("\n  📝 Test création document CCTP...")
            cctp_sql = """
            INSERT INTO technical_documents (name, type_document, content, project_id, created_by)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING id, name, type_document
            """
            
            cctp_result = await conn.execute(
                text(cctp_sql),
                "Test CCTP - Script",
                "CCTP",
                "<h1>Document de test CCTP</h1><p>Contenu de test</p>",
                project.id,
                user.id
            )
            cctp_doc = cctp_result.fetchone()
            print(f"     ✅ Document créé: ID {cctp_doc.id}, Type {cctp_doc.type_document}")
            
            # Test 2: Créer un document DPGF
            print("  📊 Test création document DPGF...")
            dpgf_sql = """
            INSERT INTO technical_documents (name, type_document, content, project_id, created_by)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING id, name, type_document
            """
            
            dpgf_result = await conn.execute(
                text(dpgf_sql),
                "Test DPGF - Script",
                "DPGF",
                "<h1>Document de test DPGF</h1><table><tr><td>Test</td></tr></table>",
                project.id,
                user.id
            )
            dpgf_doc = dpgf_result.fetchone()
            print(f"     ✅ Document créé: ID {dpgf_doc.id}, Type {dpgf_doc.type_document}")
            
            # Test 3: Associer des entreprises
            print("  🏢 Test association entreprises...")
            for i, company in enumerate(company_list[:2]):
                assoc_sql = """
                INSERT INTO technical_document_companies (technical_document_id, company_id)
                VALUES ($1, $2)
                ON CONFLICT (technical_document_id, company_id) DO NOTHING
                RETURNING id
                """
                
                assoc_result = await conn.execute(
                    text(assoc_sql),
                    cctp_doc.id,
                    company.id
                )
                assoc = assoc_result.fetchone()
                if assoc:
                    print(f"     ✅ Association créée: Doc {cctp_doc.id} <-> Entreprise {company.id}")
            
            # Test 4: Lecture avec jointures
            print("  📖 Test lecture avec jointures...")
            read_sql = """
            SELECT 
                td.id, td.name, td.type_document,
                p.name as project_name,
                u.email as creator_email,
                COUNT(tdc.company_id) as company_count
            FROM technical_documents td
            LEFT JOIN projects p ON td.project_id = p.id
            LEFT JOIN users u ON td.created_by = u.id
            LEFT JOIN technical_document_companies tdc ON td.id = tdc.technical_document_id
            WHERE td.name LIKE 'Test%'
            GROUP BY td.id, td.name, td.type_document, p.name, u.email
            """
            
            read_result = await conn.execute(text(read_sql))
            docs = read_result.fetchall()
            
            for doc in docs:
                print(f"     ✅ Document: {doc.name} ({doc.type_document})")
                print(f"        Projet: {doc.project_name}")
                print(f"        Créateur: {doc.creator_email}")
                print(f"        Entreprises: {doc.company_count}")
            
            # Test 5: Mise à jour
            print("  ✏️  Test mise à jour...")
            update_sql = """
            UPDATE technical_documents 
            SET content = $1, updated_by = $2
            WHERE id = $3
            RETURNING name, updated_at
            """
            
            update_result = await conn.execute(
                text(update_sql),
                "<h1>Contenu mis à jour</h1><p>Test de mise à jour réussi</p>",
                user.id,
                cctp_doc.id
            )
            updated_doc = update_result.fetchone()
            print(f"     ✅ Document mis à jour: {updated_doc.name} à {updated_doc.updated_at}")
            
            # Test 6: Suppression (soft delete)
            print("  🗑️  Test suppression (soft delete)...")
            delete_sql = """
            UPDATE technical_documents 
            SET is_active = false
            WHERE name LIKE 'Test%'
            RETURNING COUNT(*)
            """
            
            delete_result = await conn.execute(text(delete_sql))
            deleted_count = delete_result.scalar()
            print(f"     ✅ Documents désactivés: {deleted_count}")
            
            # Nettoyage
            print("  🧹 Nettoyage...")
            cleanup_sql = """
            DELETE FROM technical_document_companies 
            WHERE technical_document_id IN (
                SELECT id FROM technical_documents WHERE name LIKE 'Test%'
            );
            DELETE FROM technical_documents WHERE name LIKE 'Test%';
            """
            await conn.execute(text(cleanup_sql))
            print("     ✅ Données de test supprimées")
            
    except Exception as e:
        print(f"❌ Erreur lors des tests CRUD: {e}")
        return False
    finally:
        await engine.dispose()
    
    return True

async def test_api_endpoints():
    """Tester les endpoints API (simulation)"""
    print("\n🔍 Test de la configuration API...")
    
    try:
        # Vérifier que les modèles peuvent être importés
        from app.models.document import TechnicalDocument, TechnicalDocumentCompany, DocumentType
        from app.schemas.technical_document import TechnicalDocumentCreate, TechnicalDocumentResponse
        from app.api.api_v1.endpoints.technical_documents import router
        from app.services.chatgpt_service import chatgpt_service
        
        print("✅ Imports des modèles: OK")
        print("✅ Imports des schémas: OK")
        print("✅ Import du router: OK")
        print("✅ Import du service ChatGPT: OK")
        
        # Vérifier les types d'enum
        print(f"✅ Types de documents: {list(DocumentType)}")
        
        # Vérifier les prompts disponibles
        cctp_prompts = chatgpt_service.get_available_prompt_types(DocumentType.CCTP)
        dpgf_prompts = chatgpt_service.get_available_prompt_types(DocumentType.DPGF)
        
        print(f"✅ Prompts CCTP: {cctp_prompts}")
        print(f"✅ Prompts DPGF: {dpgf_prompts}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test API: {e}")
        return False

async def test_openai_configuration():
    """Tester la configuration OpenAI"""
    print("\n🔍 Test de la configuration OpenAI...")
    
    try:
        from app.core.config import settings
        
        if not settings.OPENAI_API_KEY or settings.OPENAI_API_KEY == "sk-your-openai-api-key-here":
            print("⚠️  Clé API OpenAI non configurée")
            print("   Ajoutez votre clé dans le fichier .env:")
            print("   OPENAI_API_KEY=sk-votre-cle-api-ici")
            return False
        
        print(f"✅ Clé API OpenAI configurée: {settings.OPENAI_API_KEY[:10]}...")
        print(f"✅ Modèle OpenAI: {settings.OPENAI_MODEL}")
        
        # Test basique du service (sans appel API réel)
        from app.services.chatgpt_service import chatgpt_service
        
        supported_types = chatgpt_service.get_supported_document_types()
        print(f"✅ Types de documents supportés: {supported_types}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test OpenAI: {e}")
        return False

async def main():
    """Fonction principale de test"""
    print("🧪 Tests du module Documents Techniques CCTP/DPGF")
    print("=" * 60)
    
    tests = [
        ("Structure de la base de données", test_database_structure),
        ("Opérations CRUD", test_crud_operations),
        ("Configuration API", test_api_endpoints),
        ("Configuration OpenAI", test_openai_configuration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 {test_name}...")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"💥 Erreur inattendue dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSÉ" if result else "❌ ÉCHOUÉ"
        print(f"{status:12} {test_name}")
        if result:
            passed += 1
    
    print(f"\nRésultat: {passed}/{len(results)} tests passés")
    
    if passed == len(results):
        print("\n🎉 Tous les tests sont passés ! Le module est prêt à être utilisé.")
        print("\nProchaines étapes:")
        print("1. Configurer la clé API OpenAI dans .env")
        print("2. Installer les dépendances: pip install -r requirements.txt")
        print("3. Redémarrer le serveur FastAPI")
        print("4. Tester les endpoints via l'interface Swagger: http://localhost:8000/docs")
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) ont échoué. Vérifiez la configuration.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
