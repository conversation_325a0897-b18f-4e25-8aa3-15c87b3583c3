# app/api/api_v1/endpoints/purchase_orders.py
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.models.purchase_order import PurchaseOrder, PurchaseOrderLine, Delivery, DeliveryLine
from app.schemas.purchase_order import PurchaseOrder as PurchaseOrderSchema, PurchaseOrderCreate, PurchaseOrderUpdate, PurchaseOrderLine as PurchaseOrderLineSchema, Delivery as DeliverySchema, DeliveryLine as DeliveryLineSchema

router = APIRouter()

@router.get("/", response_model=List[PurchaseOrderSchema])
def read_purchase_orders(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    company_id: int = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve purchase orders.
    """
    if company_id:
        deps.get_company_access(company_id, current_user, db)
        orders = db.query(PurchaseOrder).filter(PurchaseOrder.company_id == company_id).offset(skip).limit(limit).all()
    else:
        from app.models.company import UserCompany
        user_companies = db.query(UserCompany).filter(UserCompany.user_id == current_user.id).all()
        company_ids = [uc.company_id for uc in user_companies]
        orders = db.query(PurchaseOrder).filter(PurchaseOrder.company_id.in_(company_ids)).offset(skip).limit(limit).all()
    
    return orders

@router.post("/", response_model=PurchaseOrderSchema)
def create_purchase_order(
    *,
    db: Session = Depends(deps.get_db),
    order_in: PurchaseOrderCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new purchase order.
    """
    deps.get_company_access(order_in.company_id, current_user, db)
    
    # Check if order number already exists for this company
    existing = db.query(PurchaseOrder).filter(
        PurchaseOrder.company_id == order_in.company_id,
        PurchaseOrder.order_number == order_in.order_number
    ).first()
    if existing:
        raise HTTPException(status_code=400, detail="Order number already exists for this company")
    
    order = PurchaseOrder(**order_in.dict())
    db.add(order)
    db.commit()
    db.refresh(order)
    return order

@router.get("/{id}", response_model=PurchaseOrderSchema)
def read_purchase_order(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get purchase order by ID.
    """
    order = db.query(PurchaseOrder).filter(PurchaseOrder.id == id).first()
    if not order:
        raise HTTPException(status_code=404, detail="Purchase order not found")
    
    deps.get_company_access(order.company_id, current_user, db)
    return order

@router.put("/{id}", response_model=PurchaseOrderSchema)
def update_purchase_order(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    order_in: PurchaseOrderUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a purchase order.
    """
    order = db.query(PurchaseOrder).filter(PurchaseOrder.id == id).first()
    if not order:
        raise HTTPException(status_code=404, detail="Purchase order not found")
    
    deps.get_company_access(order.company_id, current_user, db)
    
    for field, value in order_in.dict(exclude_unset=True).items():
        setattr(order, field, value)
    
    db.add(order)
    db.commit()
    db.refresh(order)
    return order

@router.post("/{id}/lines", response_model=PurchaseOrderLineSchema)
def create_purchase_order_line(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    line_in: PurchaseOrderLineSchema,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create purchase order line.
    """
    order = db.query(PurchaseOrder).filter(PurchaseOrder.id == id).first()
    if not order:
        raise HTTPException(status_code=404, detail="Purchase order not found")
    
    deps.get_company_access(order.company_id, current_user, db)
    
    line = PurchaseOrderLine(**line_in.dict(exclude={'id'}), purchase_order_id=id)
    db.add(line)
    db.commit()
    db.refresh(line)
    return line

@router.get("/{id}/lines", response_model=List[PurchaseOrderLineSchema])
def read_purchase_order_lines(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get purchase order lines.
    """
    order = db.query(PurchaseOrder).filter(PurchaseOrder.id == id).first()
    if not order:
        raise HTTPException(status_code=404, detail="Purchase order not found")
    
    deps.get_company_access(order.company_id, current_user, db)
    
    lines = db.query(PurchaseOrderLine).filter(PurchaseOrderLine.purchase_order_id == id).all()
    return lines

@router.post("/{id}/deliveries", response_model=DeliverySchema)
def create_delivery(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    delivery_in: DeliverySchema,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create delivery for purchase order.
    """
    order = db.query(PurchaseOrder).filter(PurchaseOrder.id == id).first()
    if not order:
        raise HTTPException(status_code=404, detail="Purchase order not found")
    
    deps.get_company_access(order.company_id, current_user, db)
    
    delivery = Delivery(**delivery_in.dict(exclude={'id'}), purchase_order_id=id)
    db.add(delivery)
    db.commit()
    db.refresh(delivery)
    return delivery

@router.get("/{id}/deliveries", response_model=List[DeliverySchema])
def read_deliveries(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get deliveries for purchase order.
    """
    order = db.query(PurchaseOrder).filter(PurchaseOrder.id == id).first()
    if not order:
        raise HTTPException(status_code=404, detail="Purchase order not found")
    
    deps.get_company_access(order.company_id, current_user, db)
    
    deliveries = db.query(Delivery).filter(Delivery.purchase_order_id == id).all()
    return deliveries

@router.post("/deliveries/{delivery_id}/lines", response_model=DeliveryLineSchema)
def create_delivery_line(
    *,
    db: Session = Depends(deps.get_db),
    delivery_id: int,
    line_in: DeliveryLineSchema,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create delivery line.
    """
    delivery = db.query(Delivery).filter(Delivery.id == delivery_id).first()
    if not delivery:
        raise HTTPException(status_code=404, detail="Delivery not found")
    
    order = db.query(PurchaseOrder).filter(PurchaseOrder.id == delivery.purchase_order_id).first()
    deps.get_company_access(order.company_id, current_user, db)
    
    line = DeliveryLine(**line_in.dict(exclude={'id'}), delivery_id=delivery_id)
    db.add(line)
    db.commit()
    db.refresh(line)
    return line