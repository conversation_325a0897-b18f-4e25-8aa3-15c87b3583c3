# app/models/supplier.py
from sqlalchemy import <PERSON>olean, Column, Integer, String, DateTime, ForeignKey, Text, Numeric
from sqlalchemy.orm import relationship
from datetime import datetime
from app.core.database import Base

class Supplier(Base):
    __tablename__ = "suppliers"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    name = Column(String, nullable=False, index=True)
    code = Column(String, nullable=False, index=True)
    type = Column(String)  # supplier, subcontractor
    address = Column(Text)
    phone = Column(String)
    email = Column(String)
    website = Column(String)
    siret = Column(String)
    vat_number = Column(String)
    payment_terms = Column(String)
    is_active = Column(Boolean, default=True)
    rating = Column(Numeric(3, 2))  # 0.00 to 5.00
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    company = relationship("Company", back_populates="suppliers")
    contacts = relationship("SupplierContact", back_populates="supplier")
    purchase_orders = relationship("PurchaseOrder", back_populates="supplier")
    quotes = relationship("Quote", back_populates="supplier")
    materials = relationship("Material", back_populates="supplier")

class SupplierContact(Base):
    __tablename__ = "supplier_contacts"

    id = Column(Integer, primary_key=True, index=True)
    supplier_id = Column(Integer, ForeignKey("suppliers.id"), nullable=False)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    position = Column(String)
    phone = Column(String)
    email = Column(String)
    is_primary = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    supplier = relationship("Supplier", back_populates="contacts")