# app/middleware/rbac_middleware.py
"""
Middleware pour la gestion des permissions RBAC
"""
from typing import Dict, Any, List, Optional
from fastapi import HTTPException, status, Request, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.core.database import get_db
from app.models.user import User, UserRole
from app.models.company import UserCompany
from app.services.rbac_service import RBACService
from app.middleware.auth_sync_middleware import require_auth

class RBACMiddleware:
    """Middleware pour vérifier les permissions RBAC"""
    
    @staticmethod
    async def get_current_user_with_permissions(
        request: Request,
        db: AsyncSession = Depends(get_db)
    ) -> Dict[str, Any]:
        """
        Récupère l'utilisateur actuel avec ses permissions
        """
        # Récupérer l'utilisateur authentifié
        user_data = await require_auth(request)
        user_id = user_data.get("id")
        
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Utilisateur non authentifié"
            )
        
        # Récupérer les informations complètes de l'utilisateur
        user = await db.execute(
            select(User).where(User.id == user_id)
        )
        user = user.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Utilisateur non trouvé"
            )
        
        # Ajouter les informations de permissions
        user_data.update({
            "user_object": user,
            "system_role": user.role,
            "is_super_admin": user.role == UserRole.SUPER_ADMIN
        })
        
        return user_data
    
    @staticmethod
    async def require_system_permission(
        permission: str,
        request: Request,
        db: AsyncSession = Depends(get_db)
    ) -> Dict[str, Any]:
        """
        Vérifie qu'un utilisateur a une permission système
        """
        user_data = await RBACMiddleware.get_current_user_with_permissions(request, db)
        
        # Les super admins ont toutes les permissions système
        if user_data.get("is_super_admin"):
            return user_data
        
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Permission système requise: {permission}"
        )
    
    @staticmethod
    async def require_company_permission(
        company_id: int,
        permission: str,
        request: Request,
        db: AsyncSession = Depends(get_db)
    ) -> Dict[str, Any]:
        """
        Vérifie qu'un utilisateur a une permission spécifique dans une entreprise
        """
        user_data = await RBACMiddleware.get_current_user_with_permissions(request, db)
        user_id = user_data.get("id")
        
        # Les super admins ont toutes les permissions
        if user_data.get("is_super_admin"):
            return user_data
        
        # Vérifier la permission dans l'entreprise
        rbac_service = RBACService(db)
        has_permission = await rbac_service.check_permission(user_id, company_id, permission)
        
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission requise dans l'entreprise {company_id}: {permission}"
            )
        
        return user_data
    
    @staticmethod
    async def require_any_company_permission(
        permissions: List[str],
        request: Request,
        db: AsyncSession = Depends(get_db)
    ) -> Dict[str, Any]:
        """
        Vérifie qu'un utilisateur a au moins une des permissions dans au moins une entreprise
        """
        user_data = await RBACMiddleware.get_current_user_with_permissions(request, db)
        user_id = user_data.get("id")
        
        # Les super admins ont toutes les permissions
        if user_data.get("is_super_admin"):
            return user_data
        
        # Récupérer toutes les entreprises de l'utilisateur
        user_companies = await db.execute(
            select(UserCompany).where(
                and_(
                    UserCompany.user_id == user_id,
                    UserCompany.is_active == True
                )
            )
        )
        user_companies = user_companies.scalars().all()
        
        rbac_service = RBACService(db)
        
        # Vérifier si l'utilisateur a au moins une permission dans au moins une entreprise
        for user_company in user_companies:
            for permission in permissions:
                has_permission = await rbac_service.check_permission(
                    user_id, user_company.company_id, permission
                )
                if has_permission:
                    return user_data
        
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Au moins une de ces permissions est requise: {', '.join(permissions)}"
        )
    
    @staticmethod
    async def get_user_company_context(
        company_id: int,
        request: Request,
        db: AsyncSession = Depends(get_db)
    ) -> Dict[str, Any]:
        """
        Récupère le contexte utilisateur-entreprise avec permissions
        """
        user_data = await RBACMiddleware.get_current_user_with_permissions(request, db)
        user_id = user_data.get("id")
        
        # Les super admins ont accès à tout
        if user_data.get("is_super_admin"):
            user_data.update({
                "company_id": company_id,
                "company_role": "SUPER_ADMIN",
                "permissions": ["*"]  # Toutes les permissions
            })
            return user_data
        
        # Récupérer le rôle et les permissions dans l'entreprise
        user_company = await db.execute(
            select(UserCompany).where(
                and_(
                    UserCompany.user_id == user_id,
                    UserCompany.company_id == company_id,
                    UserCompany.is_active == True
                )
            )
        )
        user_company = user_company.scalar_one_or_none()
        
        if not user_company:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Accès refusé à l'entreprise {company_id}"
            )
        
        # Récupérer les permissions
        rbac_service = RBACService(db)
        permissions = await rbac_service.get_user_permissions(user_id, company_id)
        
        user_data.update({
            "company_id": company_id,
            "company_role": user_company.role_name,
            "permissions": permissions
        })
        
        return user_data

# Fonctions de dépendance pour FastAPI
async def require_super_admin(
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """Dependency pour exiger un rôle super admin"""
    return await RBACMiddleware.require_system_permission("super_admin", request, db)

def require_company_permission(permission: str):
    """Factory pour créer une dépendance de permission d'entreprise"""
    async def permission_checker(
        company_id: int,
        request: Request,
        db: AsyncSession = Depends(get_db)
    ) -> Dict[str, Any]:
        return await RBACMiddleware.require_company_permission(company_id, permission, request, db)
    
    return permission_checker

def require_any_permission(permissions: List[str]):
    """Factory pour créer une dépendance de permissions multiples"""
    async def permission_checker(
        request: Request,
        db: AsyncSession = Depends(get_db)
    ) -> Dict[str, Any]:
        return await RBACMiddleware.require_any_company_permission(permissions, request, db)
    
    return permission_checker

async def get_user_context(
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """Récupère le contexte utilisateur avec permissions"""
    return await RBACMiddleware.get_current_user_with_permissions(request, db)

def get_company_context(company_id: int):
    """Factory pour récupérer le contexte utilisateur-entreprise"""
    async def context_getter(
        request: Request,
        db: AsyncSession = Depends(get_db)
    ) -> Dict[str, Any]:
        return await RBACMiddleware.get_user_company_context(company_id, request, db)
    
    return context_getter
