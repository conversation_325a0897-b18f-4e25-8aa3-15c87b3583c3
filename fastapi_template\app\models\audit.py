# app/models/audit.py
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String, DateTime, ForeignKey, Text, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from app.core.database import Base


class AuditAction(str, enum.Enum):
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    LOGIN = "login"
    LOGOUT = "logout"
    INVITE = "invite"
    ACCEPT_INVITE = "accept_invite"
    REJECT_INVITE = "reject_invite"
    CHANGE_ROLE = "change_role"
    EXPORT = "export"
    IMPORT = "import"


class AuditLog(Base):
    """
    Table d'audit pour tracer toutes les actions importantes
    """
    __tablename__ = "audit_logs"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    action = Column(String, nullable=False)  # AuditAction enum
    resource_type = Column(String, nullable=False)  # projects, users, companies, etc.
    resource_id = Column(String, nullable=True)  # ID of the affected resource
    details = Column(JSON, nullable=True)  # Additional details about the action
    ip_address = Column(String, nullable=True)
    user_agent = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)

    # Relationships
    company = relationship("Company")
    user = relationship("User")

    def __repr__(self):
        return f"<AuditLog(action={self.action}, resource={self.resource_type}:{self.resource_id}, user={self.user_id})>"


class CompanyInvitation(Base):
    """
    Table pour gérer les invitations d'utilisateurs dans les entreprises
    """
    __tablename__ = "company_invitations"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    email = Column(String, nullable=False, index=True)
    role = Column(String, nullable=False, default="user")  # CompanyRole enum
    invited_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    invitation_token = Column(String, unique=True, nullable=False, index=True)
    expires_at = Column(DateTime, nullable=False)
    accepted_at = Column(DateTime, nullable=True)
    rejected_at = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    company = relationship("Company")
    inviter = relationship("User")

    @property
    def is_expired(self) -> bool:
        """Check if invitation is expired"""
        return datetime.utcnow() > self.expires_at

    @property
    def is_pending(self) -> bool:
        """Check if invitation is still pending"""
        return (
            self.is_active and 
            not self.accepted_at and 
            not self.rejected_at and 
            not self.is_expired
        )

    def __repr__(self):
        return f"<CompanyInvitation(email={self.email}, company={self.company_id}, status={'pending' if self.is_pending else 'processed'})>"
