'use client'

import React, { useRef, useState, useEffect } from 'react'
import { Editor } from '@tinymce/tinymce-react'
import { DocumentType } from '@/types/technical-document'

interface TechnicalDocumentEditorProps {
  value: string
  onChange: (content: string) => void
  documentType: DocumentType
  onTextSelection?: (selectedText: string) => void
  readOnly?: boolean
  placeholder?: string
}

export default function TechnicalDocumentEditor({
  value,
  onChange,
  documentType,
  onTextSelection,
  readOnly = false,
  placeholder = ''
}: TechnicalDocumentEditorProps) {
  const editorRef = useRef<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [initialValue, setInitialValue] = useState(value)
  const changeTimeoutRef = useRef<NodeJS.Timeout>()
  const isInitializedRef = useRef(false)

  // Mettre à jour la valeur initiale seulement si l'éditeur n'est pas encore initialisé
  useEffect(() => {
    if (!isInitializedRef.current) {
      setInitialValue(value)
    }
  }, [value])

  // Fonction de changement avec debounce pour éviter les sauvegardes trop fréquentes
  const handleContentChange = (content: string) => {
    // Marquer comme initialisé après le premier changement
    isInitializedRef.current = true

    if (changeTimeoutRef.current) {
      clearTimeout(changeTimeoutRef.current)
    }

    changeTimeoutRef.current = setTimeout(() => {
      onChange(content)
    }, 300) // Réduire le délai pour une meilleure réactivité
  }

  // Configuration TinyMCE adaptée aux documents techniques
  const editorConfig = {
    height: 600,
    menubar: true,
    plugins: [
      'advlist', 'autolink', 'lists', 'link', 'charmap', 'preview',
      'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
      'table', 'help', 'wordcount', 'powerpaste', 'importword'
    ],
    toolbar: documentType === DocumentType.CCTP
      ? 'undo redo | importword | blocks | bold italic underline | alignleft aligncenter alignright | outdent indent | numlist bullist | forecolor backcolor | charmap | fullscreen preview | link'
      : 'undo redo | importword | blocks | bold italic underline | alignleft aligncenter alignright | outdent indent | numlist bullist | table | forecolor backcolor | fullscreen preview | link',
    
    content_style: `
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 14px;
        line-height: 1.6;
        color: #333;
        margin: 0;
        padding: 30px;
        background: white;
      }
      h1, h2, h3, h4, h5, h6 { 
        color: #2563eb; 
        margin-top: 1.5em; 
        margin-bottom: 0.5em; 
      }
      h1 { font-size: 24px; border-bottom: 2px solid #2563eb; padding-bottom: 8px; }
      h2 { font-size: 20px; border-bottom: 1px solid #e5e7eb; padding-bottom: 4px; }
      h3 { font-size: 18px; }
      table { border-collapse: collapse; width: 100%; margin: 1em 0; }
      table td, table th { border: 1px solid #d1d5db; padding: 8px; }
      table th { background-color: #f3f4f6; font-weight: bold; }
      .technical-spec { background-color: #f0f9ff; border-left: 4px solid #0ea5e9; padding: 12px; margin: 1em 0; }
      .important-note { background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 12px; margin: 1em 0; }
      .warning { background-color: #fee2e2; border-left: 4px solid #ef4444; padding: 12px; margin: 1em 0; }
    `,
    
    // Configuration spécifique aux documents techniques
    formats: {
      technical_spec: {
        block: 'div',
        classes: 'technical-spec',
        wrapper: true
      },
      important_note: {
        block: 'div', 
        classes: 'important-note',
        wrapper: true
      },
      warning: {
        block: 'div',
        classes: 'warning', 
        wrapper: true
      }
    },

    style_formats: [
      { title: 'Spécification technique', format: 'technical_spec' },
      { title: 'Note importante', format: 'important_note' },
      { title: 'Avertissement', format: 'warning' },
      { title: 'Titre principal', block: 'h1' },
      { title: 'Titre section', block: 'h2' },
      { title: 'Titre sous-section', block: 'h3' },
      { title: 'Paragraphe', block: 'p' }
    ],

    // Templates pour documents techniques
    templates: documentType === DocumentType.CCTP ? [
      {
        title: 'Article CCTP standard',
        description: 'Structure d\'article CCTP',
        content: `
          <h3>Article X.X - [Titre de l'article]</h3>
          <div class="technical-spec">
            <h4>Spécifications techniques</h4>
            <p>[Détails techniques]</p>
          </div>
          <h4>Mise en œuvre</h4>
          <p>[Conditions d'exécution]</p>
          <h4>Contrôles et réception</h4>
          <p>[Modalités de contrôle]</p>
        `
      }
    ] : [
      {
        title: 'Poste DPGF standard',
        description: 'Structure de poste DPGF',
        content: `
          <h3>[Code poste] - [Désignation]</h3>
          <table>
            <tr><th>Désignation</th><th>Unité</th><th>Quantité</th><th>Prix unitaire</th><th>Prix total</th></tr>
            <tr><td>[Description détaillée]</td><td>[m², m³, ml, u, etc.]</td><td>[Quantité]</td><td>[Prix HT]</td><td>[Total HT]</td></tr>
          </table>
        `
      }
    ],

    // Configuration de l'interface
    branding: false,
    promotion: false,
    resize: false,
    statusbar: true,
    elementpath: true,

    // Éviter les erreurs d'événements passifs
    mobile: {
      theme: 'silver'
    },
    
    // Configuration PowerPaste pour l'import Word
    powerpaste_word_import: 'clean',
    powerpaste_html_import: 'clean',
    paste_data_images: true,
    paste_retain_style_properties: 'color font-size font-family background-color',
    paste_remove_styles_if_webkit: false,
    
    // Configuration des liens et images
    link_default_target: '_blank',
    image_advtab: true,
    image_caption: true,
    
    // Sauvegarde automatique désactivée pour éviter les conflits
    autosave_ask_before_unload: false,
    autosave_interval: false,
    
    // Accessibilité
    a11y_advanced_options: true,
    
    // Menu contextuel personnalisé avec options ChatGPT
    contextmenu: 'enhance_text improve_text simplify_text develop_text | copy paste | selectall',
    
    // Validation du contenu
    verify_html: false,
    
    // Configuration mobile
    mobile: {
      theme: 'silver',
      plugins: ['autosave', 'lists', 'autolink'],
      toolbar: 'undo redo | bold italic | bullist numlist | link'
    },

    // Callbacks
    setup: (editor: any) => {
      // Gestion de la sélection de texte pour le menu contextuel IA avec debounce
      let selectionTimeout: NodeJS.Timeout
      editor.on('mouseup keyup', () => {
        clearTimeout(selectionTimeout)
        selectionTimeout = setTimeout(() => {
          const selectedText = editor.selection.getContent({ format: 'text' })
          if (selectedText && selectedText.trim().length > 0 && onTextSelection) {
            onTextSelection(selectedText.trim())
          }
        }, 100)
      })

      // Bouton d'import Word personnalisé
      editor.ui.registry.addButton('importword', {
        icon: 'upload',
        text: 'Word',
        tooltip: 'Importer un document Word (remplace le contenu ou insère à la position du curseur)',
        onAction: () => {
          // Créer un input file invisible
          const input = document.createElement('input')
          input.type = 'file'
          input.accept = '.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          input.style.display = 'none'

          input.onchange = async (e: any) => {
            const file = e.target.files[0]
            if (file) {
              try {
                // Demander à l'utilisateur s'il veut remplacer ou insérer
                const currentContent = editor.getContent({ format: 'text' }).trim()
                let shouldReplace = true

                if (currentContent.length > 0) {
                  shouldReplace = confirm(
                    `Le document contient déjà du texte.\n\n` +
                    `Voulez-vous remplacer tout le contenu par "${file.name}" ?\n\n` +
                    '• Cliquez "OK" pour REMPLACER tout le contenu\n' +
                    '• Cliquez "Annuler" pour INSÉRER à la position du curseur'
                  )
                }

                await handleWordImport(file, editor, shouldReplace)
              } catch (error) {
                console.error('Erreur lors de l\'import Word:', error)
                alert('Erreur lors de l\'import du fichier Word')
              }
            }
          }

          document.body.appendChild(input)
          input.click()
          document.body.removeChild(input)
        }
      })

      // Boutons de menu contextuel pour l'amélioration IA
      editor.ui.registry.addMenuItem('enhance_text', {
        text: '✨ Améliorer le texte',
        icon: 'edit-block',
        onAction: () => {
          const selectedText = editor.selection.getContent({ format: 'text' })
          if (selectedText && selectedText.trim()) {
            handleAIEnhancement(editor, selectedText, 'enhance')
          } else {
            alert('Veuillez sélectionner du texte à améliorer')
          }
        }
      })

      editor.ui.registry.addMenuItem('improve_text', {
        text: '🔄 Reformuler',
        icon: 'refresh',
        onAction: () => {
          const selectedText = editor.selection.getContent({ format: 'text' })
          if (selectedText && selectedText.trim()) {
            handleAIEnhancement(editor, selectedText, 'improve')
          } else {
            alert('Veuillez sélectionner du texte à reformuler')
          }
        }
      })

      editor.ui.registry.addMenuItem('simplify_text', {
        text: '📝 Simplifier',
        icon: 'remove-formatting',
        onAction: () => {
          const selectedText = editor.selection.getContent({ format: 'text' })
          if (selectedText && selectedText.trim()) {
            handleAIEnhancement(editor, selectedText, 'simplify')
          } else {
            alert('Veuillez sélectionner du texte à simplifier')
          }
        }
      })

      editor.ui.registry.addMenuItem('develop_text', {
        text: '📖 Développer',
        icon: 'plus',
        onAction: () => {
          const selectedText = editor.selection.getContent({ format: 'text' })
          if (selectedText && selectedText.trim()) {
            handleAIEnhancement(editor, selectedText, 'develop')
          } else {
            alert('Veuillez sélectionner du texte à développer')
          }
        }
      })

      // Ajout de boutons personnalisés pour l'IA (toolbar)
      editor.ui.registry.addButton('enhance_ai', {
        text: 'IA',
        tooltip: 'Améliorer avec l\'IA',
        onAction: () => {
          const selectedText = editor.selection.getContent({ format: 'text' })
          if (selectedText && onTextSelection) {
            onTextSelection(selectedText)
          }
        }
      })

      // Raccourcis clavier
      editor.addShortcut('ctrl+shift+a', 'Améliorer avec IA', () => {
        const selectedText = editor.selection.getContent({ format: 'text' })
        if (selectedText && onTextSelection) {
          onTextSelection(selectedText)
        }
      })

      // Le menu contextuel est maintenant géré directement par TinyMCE
      // avec les options personnalisées définies dans contextmenu
    },

    init_instance_callback: (editor: any) => {
      setIsLoading(false)
      editorRef.current = editor
    }
  }

  // Fonction pour gérer l'amélioration IA directement dans TinyMCE
  const handleAIEnhancement = async (editor: any, selectedText: string, promptType: string) => {
    try {
      // Afficher un indicateur de chargement
      const loadingText = '<span style="color: #3b82f6; font-style: italic;">⏳ Amélioration en cours...</span>'
      editor.selection.setContent(loadingText)

      // Appeler l'API ChatGPT
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
      const token = localStorage.getItem('token')

      const response = await fetch(`${API_BASE_URL}/api/v1/technical-documents/enhance-text`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          text: selectedText,
          prompt_type: promptType,
          document_type: documentType,
          context: `Document technique ${documentType}`
        })
      })

      if (!response.ok) {
        throw new Error('Erreur lors de l\'amélioration du texte')
      }

      const data = await response.json()
      const enhancedText = data.enhanced_text || data.result || selectedText

      // Remplacer le texte de chargement par le texte amélioré
      editor.selection.setContent(enhancedText)

      // Déclencher onChange pour sauvegarder
      if (onChange) {
        const newContent = editor.getContent()
        onChange(newContent)
      }

    } catch (error) {
      console.error('Erreur lors de l\'amélioration:', error)
      // Remettre le texte original en cas d'erreur
      editor.selection.setContent(selectedText)
      alert('Erreur lors de l\'amélioration du texte. Veuillez réessayer.')
    }
  }

  // Fonction pour insérer du texte amélioré
  const insertEnhancedText = (enhancedText: string) => {
    if (editorRef.current) {
      editorRef.current.selection.setContent(enhancedText)
    }
  }

  // Fonction pour gérer l'import de fichiers Word
  const handleWordImport = async (file: File, editor: any, shouldReplace: boolean = true) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()

      reader.onload = async (e) => {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer
          let cleanHtml = ''

          // Pour les fichiers .docx, on peut utiliser mammoth.js
          if (file.name.endsWith('.docx')) {
            // Dynamically import mammoth only when needed
            const mammoth = await import('mammoth')
            const result = await mammoth.convertToHtml({ arrayBuffer })

            // Nettoyer le HTML
            cleanHtml = cleanWordHtml(result.value)
          } else {
            // Pour les anciens formats .doc, on peut essayer une conversion basique
            const text = new TextDecoder().decode(arrayBuffer)
            cleanHtml = `<p>${text.replace(/\n/g, '</p><p>')}</p>`
          }

          // Insérer ou remplacer le contenu selon le choix de l'utilisateur
          if (shouldReplace) {
            // Remplacer tout le contenu
            editor.setContent(cleanHtml)
          } else {
            // Insérer à la position du curseur
            editor.insertContent(cleanHtml)
          }

          // Déclencher onChange pour sauvegarder
          if (onChange) {
            const newContent = editor.getContent()
            onChange(newContent)
          }

          resolve(cleanHtml)
        } catch (error) {
          reject(error)
        }
      }

      reader.onerror = () => reject(new Error('Erreur de lecture du fichier'))
      reader.readAsArrayBuffer(file)
    })
  }

  // Fonction pour nettoyer le HTML provenant de Word
  const cleanWordHtml = (html: string): string => {
    return html
      // Préserver les titres importants
      .replace(/<h([1-6])[^>]*>/g, '<h$1>')
      // Supprimer les styles inline inutiles mais préserver certains
      .replace(/style="[^"]*(?:mso-|font-family: 'Times New Roman'|font-family: Calibri)[^"]*"/g, '')
      // Préserver les styles de couleur et de taille importants
      .replace(/style="([^"]*(?:color|font-size|font-weight|text-align)[^"]*)"/g, 'style="$1"')
      // Supprimer les classes Word spécifiques
      .replace(/class="[^"]*(?:MsoNormal|MsoTitle|MsoHeading)[^"]*"/g, '')
      // Nettoyer les spans vides
      .replace(/<span[^>]*><\/span>/g, '')
      // Préserver les divs qui contiennent du contenu structuré, sinon les convertir en paragraphes
      .replace(/<div([^>]*)>(\s*)<\/div>/g, '') // Supprimer les divs vides
      .replace(/<div[^>]*>/g, '<p>')
      .replace(/<\/div>/g, '</p>')
      // Nettoyer les paragraphes vides mais préserver les sauts de ligne intentionnels
      .replace(/<p[^>]*>(\s|&nbsp;)*<\/p>/g, '')
      // Préserver les listes
      .replace(/<o:p[^>]*>/g, '')
      .replace(/<\/o:p>/g, '')
      // Nettoyer les espaces multiples mais préserver la structure
      .replace(/\s+/g, ' ')
      .replace(/>\s+</g, '><')
      .trim()
  }

  // Exposer la fonction d'insertion pour le composant parent
  useEffect(() => {
    if (editorRef.current) {
      (editorRef.current as any).insertEnhancedText = insertEnhancedText
    }
  }, [])

  // Nettoyer les timeouts au démontage
  useEffect(() => {
    return () => {
      if (changeTimeoutRef.current) {
        clearTimeout(changeTimeoutRef.current)
      }
    }
  }, [])

  return (
    <div className="relative">
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-600">Chargement de l'éditeur...</span>
          </div>
        </div>
      )}
      
      <Editor
        apiKey={process.env.NEXT_PUBLIC_TINYMCE_API_KEY}
        onInit={(evt, editor) => {
          editorRef.current = editor
          setIsLoading(false)
        }}
        initialValue={initialValue}
        onEditorChange={handleContentChange}
        init={{
          ...editorConfig,
          readonly: readOnly
        }}
      />
    </div>
  )
}
