'use client'

import React, { useRef, useState, useEffect } from 'react'
import { Editor } from '@tinymce/tinymce-react'
import { DocumentType } from '@/types/technical-document'

interface TechnicalDocumentEditorProps {
  value: string
  onChange: (content: string) => void
  documentType: DocumentType
  onTextSelection?: (selectedText: string) => void
  readOnly?: boolean
  placeholder?: string
}

export default function TechnicalDocumentEditor({
  value,
  onChange,
  documentType,
  onTextSelection,
  readOnly = false,
  placeholder = 'Commencez à rédiger votre document...'
}: TechnicalDocumentEditorProps) {
  const editorRef = useRef<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const changeTimeoutRef = useRef<NodeJS.Timeout>()

  // Fonction de changement avec debounce pour éviter les sauvegardes trop fréquentes
  const handleContentChange = (content: string) => {
    if (changeTimeoutRef.current) {
      clearTimeout(changeTimeoutRef.current)
    }

    changeTimeoutRef.current = setTimeout(() => {
      onChange(content)
    }, 500) // Attendre 500ms avant de déclencher onChange
  }

  // Configuration TinyMCE adaptée aux documents techniques
  const editorConfig = {
    height: 600,
    menubar: true,
    plugins: [
      'advlist', 'autolink', 'lists', 'link', 'charmap', 'preview',
      'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
      'table', 'help', 'wordcount'
    ],
    toolbar: documentType === DocumentType.CCTP
      ? 'undo redo | blocks | bold italic underline | alignleft aligncenter alignright | outdent indent | numlist bullist | forecolor backcolor | charmap | fullscreen preview | link'
      : 'undo redo | blocks | bold italic underline | alignleft aligncenter alignright | outdent indent | numlist bullist | table | forecolor backcolor | fullscreen preview | link',
    
    content_style: `
      body { 
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
        font-size: 14px; 
        line-height: 1.6;
        color: #333;
        max-width: 210mm;
        margin: 0 auto;
        padding: 20mm;
        background: white;
      }
      h1, h2, h3, h4, h5, h6 { 
        color: #2563eb; 
        margin-top: 1.5em; 
        margin-bottom: 0.5em; 
      }
      h1 { font-size: 24px; border-bottom: 2px solid #2563eb; padding-bottom: 8px; }
      h2 { font-size: 20px; border-bottom: 1px solid #e5e7eb; padding-bottom: 4px; }
      h3 { font-size: 18px; }
      table { border-collapse: collapse; width: 100%; margin: 1em 0; }
      table td, table th { border: 1px solid #d1d5db; padding: 8px; }
      table th { background-color: #f3f4f6; font-weight: bold; }
      .technical-spec { background-color: #f0f9ff; border-left: 4px solid #0ea5e9; padding: 12px; margin: 1em 0; }
      .important-note { background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 12px; margin: 1em 0; }
      .warning { background-color: #fee2e2; border-left: 4px solid #ef4444; padding: 12px; margin: 1em 0; }
    `,
    
    // Configuration spécifique aux documents techniques
    formats: {
      technical_spec: {
        block: 'div',
        classes: 'technical-spec',
        wrapper: true
      },
      important_note: {
        block: 'div', 
        classes: 'important-note',
        wrapper: true
      },
      warning: {
        block: 'div',
        classes: 'warning', 
        wrapper: true
      }
    },

    style_formats: [
      { title: 'Spécification technique', format: 'technical_spec' },
      { title: 'Note importante', format: 'important_note' },
      { title: 'Avertissement', format: 'warning' },
      { title: 'Titre principal', block: 'h1' },
      { title: 'Titre section', block: 'h2' },
      { title: 'Titre sous-section', block: 'h3' },
      { title: 'Paragraphe', block: 'p' }
    ],

    // Templates pour documents techniques
    templates: documentType === DocumentType.CCTP ? [
      {
        title: 'Article CCTP standard',
        description: 'Structure d\'article CCTP',
        content: `
          <h3>Article X.X - [Titre de l'article]</h3>
          <div class="technical-spec">
            <h4>Spécifications techniques</h4>
            <p>[Détails techniques]</p>
          </div>
          <h4>Mise en œuvre</h4>
          <p>[Conditions d'exécution]</p>
          <h4>Contrôles et réception</h4>
          <p>[Modalités de contrôle]</p>
        `
      }
    ] : [
      {
        title: 'Poste DPGF standard',
        description: 'Structure de poste DPGF',
        content: `
          <h3>[Code poste] - [Désignation]</h3>
          <table>
            <tr><th>Désignation</th><th>Unité</th><th>Quantité</th><th>Prix unitaire</th><th>Prix total</th></tr>
            <tr><td>[Description détaillée]</td><td>[m², m³, ml, u, etc.]</td><td>[Quantité]</td><td>[Prix HT]</td><td>[Total HT]</td></tr>
          </table>
        `
      }
    ],

    // Configuration de l'interface
    branding: false,
    promotion: false,
    resize: false,
    statusbar: true,
    elementpath: true,

    // Éviter les erreurs d'événements passifs
    mobile: {
      theme: 'silver'
    },
    
    // Gestion du contenu - configuration de base sans plugin paste
    paste_data_images: false,
    paste_as_text: true,
    
    // Configuration des liens et images
    link_default_target: '_blank',
    image_advtab: true,
    image_caption: true,
    
    // Sauvegarde automatique désactivée pour éviter les conflits
    autosave_ask_before_unload: false,
    autosave_interval: false,
    
    // Accessibilité
    a11y_advanced_options: true,
    
    // Configuration du menu contextuel
    contextmenu: 'link image table | copy | selectall',
    
    // Validation du contenu
    verify_html: false,
    
    // Configuration mobile
    mobile: {
      theme: 'silver',
      plugins: ['autosave', 'lists', 'autolink'],
      toolbar: 'undo redo | bold italic | bullist numlist | link'
    },

    // Callbacks
    setup: (editor: any) => {
      // Gestion de la sélection de texte pour le menu contextuel IA avec debounce
      let selectionTimeout: NodeJS.Timeout
      editor.on('mouseup keyup', () => {
        clearTimeout(selectionTimeout)
        selectionTimeout = setTimeout(() => {
          const selectedText = editor.selection.getContent({ format: 'text' })
          if (selectedText && selectedText.trim().length > 0 && onTextSelection) {
            onTextSelection(selectedText.trim())
          }
        }, 100)
      })

      // Ajout de boutons personnalisés pour l'IA
      editor.ui.registry.addButton('enhance_ai', {
        text: 'IA',
        tooltip: 'Améliorer avec l\'IA',
        onAction: () => {
          const selectedText = editor.selection.getContent({ format: 'text' })
          if (selectedText && onTextSelection) {
            onTextSelection(selectedText)
          }
        }
      })

      // Raccourcis clavier
      editor.addShortcut('ctrl+shift+a', 'Améliorer avec IA', () => {
        const selectedText = editor.selection.getContent({ format: 'text' })
        if (selectedText && onTextSelection) {
          onTextSelection(selectedText)
        }
      })
    },

    init_instance_callback: (editor: any) => {
      setIsLoading(false)
      editorRef.current = editor
    }
  }

  // Fonction pour insérer du texte amélioré
  const insertEnhancedText = (enhancedText: string) => {
    if (editorRef.current) {
      editorRef.current.selection.setContent(enhancedText)
    }
  }

  // Exposer la fonction d'insertion pour le composant parent
  useEffect(() => {
    if (editorRef.current) {
      (editorRef.current as any).insertEnhancedText = insertEnhancedText
    }
  }, [])

  // Nettoyer les timeouts au démontage
  useEffect(() => {
    return () => {
      if (changeTimeoutRef.current) {
        clearTimeout(changeTimeoutRef.current)
      }
    }
  }, [])

  return (
    <div className="relative">
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-600">Chargement de l'éditeur...</span>
          </div>
        </div>
      )}
      
      <Editor
        apiKey={process.env.NEXT_PUBLIC_TINYMCE_API_KEY}
        onInit={(evt, editor) => {
          editorRef.current = editor
          setIsLoading(false)
        }}
        value={value}
        onEditorChange={handleContentChange}
        init={{
          ...editorConfig,
          readonly: readOnly,
          placeholder: placeholder
        }}
      />
    </div>
  )
}
