# app/api/api_v1/endpoints/admin_users.py
from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request
from pydantic import BaseModel, EmailStr, Field
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, text, and_
from datetime import datetime
import uuid

from app.services.supabase_service import SupabaseService
from app.middleware.auth_sync_middleware import require_auth
from app.core.database import get_db
from app.models.user import User, UserRole
from app.models.company import Company, UserCompany, CompanyRole
from app.models.rbac import Role, Permission, CompanyRolePermission
from app.schemas.user import UserWithCompanies, UserPermissionSummary
from app.schemas.company import CompanyRoleAssignment, CompanyWithUsers

router = APIRouter()

# Dependency pour vérifier les droits super admin
async def require_super_admin(request: Request) -> Dict[str, Any]:
    """Dependency pour exiger un rôle super admin"""
    user = await require_auth(request)
    user_role = user.get("role", "").upper()
    is_superuser = user.get("is_superuser", False)

    if user_role != "SUPER_ADMIN" and not is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Super admin access required"
        )
    return user

# Pydantic models
class AdminUserResponse(BaseModel):
    id: str
    email: str
    first_name: str
    last_name: str
    role: str
    is_active: bool
    is_verified: bool
    created_at: str
    last_sign_in_at: Optional[str] = None
    company_role: str
    user_metadata: Optional[Dict] = None

class AdminUserCreate(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: str
    role: str = Field(..., description="Rôle système de l'utilisateur")
    company_id: int
    company_role: str = Field("USER", description="Rôle dans l'entreprise")

class AdminUserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    role: Optional[str] = None
    company_role: Optional[str] = None
    is_active: Optional[bool] = None

class UserStatusToggle(BaseModel):
    is_active: bool

class UserRoleUpdate(BaseModel):
    """Mise à jour du rôle d'un utilisateur dans une entreprise"""
    user_id: int
    company_id: int
    role_name: str = Field(..., description="Nouveau rôle dans l'entreprise")

class CompanyUsersResponse(BaseModel):
    """Réponse avec les utilisateurs d'une entreprise"""
    company_id: int
    company_name: str
    users: List[Dict[str, Any]] = []
    total_users: int = 0

@router.get("/", response_model=List[AdminUserResponse])
async def list_company_admins(
    request: Request,
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """
    Lister tous les administrateurs d'entreprises (exclut les super admins)
    """
    try:
        supabase_service = SupabaseService()
        users_data = await supabase_service.list_users()

        # Transformer les données Supabase en format AdminUserResponse
        # Exclure les super admins - ils ne doivent pas apparaître dans cette liste
        users = []
        for user in users_data.get("users", []):
            user_role = user.get("user_metadata", {}).get("role", "user").lower()

            # Exclure les super admins de la liste
            if user_role == "SUPER_ADMIN":
                continue

            users.append(AdminUserResponse(
                id=user.get("id"),
                email=user.get("email", ""),
                first_name=user.get("user_metadata", {}).get("first_name", ""),
                last_name=user.get("user_metadata", {}).get("last_name", ""),
                role=user.get("user_metadata", {}).get("role", "user"),
                is_active=not user.get("banned_until"),
                is_verified=bool(user.get("email_confirmed_at")),
                created_at=user.get("created_at", ""),
                last_sign_in_at=user.get("last_sign_in_at"),
                company_role=user.get("user_metadata", {}).get("role", "user"),
                user_metadata=user.get("user_metadata", {})
            ))

        return users
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch users: {str(e)}"
        )

@router.get("/company/{company_id}", response_model=List[AdminUserResponse])
async def list_company_users(
    company_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """
    Lister les administrateurs d'une entreprise spécifique
    """
    try:
        from app.models.company import UserCompany
        from app.models.user import User
        from sqlalchemy import select

        print(f"🔍 Recherche des utilisateurs pour l'entreprise {company_id}")

        # Joindre UserCompany avec User pour récupérer les détails
        result = await db.execute(
            select(User, UserCompany)
            .join(UserCompany, User.id == UserCompany.user_id)
            .where(UserCompany.company_id == company_id)
        )
        user_data = result.all()

        print(f"🔍 Trouvé {len(user_data)} utilisateurs pour l'entreprise {company_id}")

        if not user_data:
            return []

        users = []
        for user, user_company in user_data:
            # Exclure les super admins
            if user.role and user.role.upper() == "SUPER_ADMIN":
                print(f"🔍 Exclusion du super admin: {user.email}")
                continue

            print(f"🔍 Ajout de l'utilisateur: {user.email}, rôle: {user.role}")

            # Convertir les enums en strings
            user_role_str = user.role.value if hasattr(user.role, 'value') else str(user.role) if user.role else "user"
            company_role_str = user_company.role.value if hasattr(user_company.role, 'value') else str(user_company.role) if user_company.role else "user"

            users.append(AdminUserResponse(
                id=str(user.id),  # Convertir en string pour compatibilité
                email=user.email or "",
                first_name=user.first_name or "",
                last_name=user.last_name or "",
                role=user_role_str,
                is_active=user.is_active if user.is_active is not None else True,
                is_verified=user.is_verified if user.is_verified is not None else False,
                created_at=user.created_at.isoformat() if user.created_at else "",
                last_sign_in_at=None,  # Pas disponible dans la base locale
                company_role=company_role_str,
                user_metadata={}
            ))

        print(f"✅ Retour de {len(users)} utilisateurs pour l'entreprise {company_id}")
        return users

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch company users: {str(e)}"
        )

@router.post("/", response_model=AdminUserResponse)
async def create_user(
    user_data: AdminUserCreate,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """
    Créer un nouvel utilisateur et l'associer à une entreprise
    """
    try:
        from app.models.user import User
        from app.models.company import UserCompany, Company
        from sqlalchemy import select

        print(f"🔄 Création utilisateur: {user_data.email} pour entreprise {user_data.company_id}")

        # 1. Vérifier que l'entreprise existe
        company_result = await db.execute(select(Company).where(Company.id == user_data.company_id))
        company = company_result.scalar_one_or_none()
        if not company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Company with id {user_data.company_id} not found"
            )

        # 2. Créer l'utilisateur dans Supabase
        supabase_service = SupabaseService()
        user_metadata = {
            "first_name": user_data.first_name,
            "last_name": user_data.last_name,
            "role": user_data.role
        }

        supabase_user_id = None
        try:
            supabase_result = await supabase_service.create_user(
                email=user_data.email,
                password=user_data.password,
                user_metadata=user_metadata
            )

            # Extract user ID from Supabase response
            if "user" in supabase_result:
                supabase_user = supabase_result["user"]
            else:
                supabase_user = supabase_result

            supabase_user_id = supabase_user.get("id")
            print(f"✅ Utilisateur créé dans Supabase: {supabase_user_id}")

        except Exception as supabase_error:
            error_msg = str(supabase_error)
            print(f"❌ Erreur Supabase: {error_msg}")

            # Gestion des erreurs spécifiques de Supabase
            if "email_exists" in error_msg or "already been registered" in error_msg:
                print(f"⚠️ L'utilisateur existe déjà dans Supabase, on continue avec la création locale")
                # On continue sans supabase_user_id pour créer seulement l'utilisateur local
                supabase_user_id = None
            elif "weak_password" in error_msg or "password" in error_msg.lower():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Le mot de passe ne respecte pas les critères de sécurité (minimum 6 caractères)"
                )
            elif "invalid_email" in error_msg or "email" in error_msg.lower():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"L'adresse email '{user_data.email}' n'est pas valide"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Erreur lors de la création du compte utilisateur: {error_msg}"
                )

        # 3. Convertir le rôle string vers la valeur enum PostgreSQL
        # Mapping des rôles frontend vers valeurs PostgreSQL (MAJUSCULES)
        role_mapping = {
            "ADMIN": "ADMIN",
            "admin": "ADMIN",
            "CHEF_PROJET": "CHEF_PROJET",
            "chef_projet": "CHEF_PROJET",
            "EMPLOYE": "EMPLOYE",
            "employe": "EMPLOYE",
            "CLIENT": "CLIENT",
            "client": "CLIENT",
            "SUPER_ADMIN": "SUPER_ADMIN",
            "super_admin": "SUPER_ADMIN"
        }

        user_role_value = role_mapping.get(user_data.role, "EMPLOYE")
        print(f"🔄 Conversion rôle: '{user_data.role}' -> '{user_role_value}'")

        # 4. Créer l'utilisateur dans la base locale avec raw SQL pour forcer l'enum
        # Utiliser une requête SQL brute pour éviter les problèmes d'enum SQLAlchemy
        insert_query = text("""
            INSERT INTO users (
                supabase_user_id, email, first_name, last_name, role,
                is_active, is_verified, created_at, updated_at
            ) VALUES (
                :supabase_user_id, :email, :first_name, :last_name, :role,
                :is_active, :is_verified, NOW(), NOW()
            ) RETURNING id
        """)

        result = await db.execute(insert_query, {
            "supabase_user_id": supabase_user_id,
            "email": user_data.email,
            "first_name": user_data.first_name,
            "last_name": user_data.last_name,
            "role": user_role_value,  # Utiliser la valeur string
            "is_active": True,
            "is_verified": True
        })

        user_id = result.scalar()
        print(f"✅ Utilisateur créé avec ID: {user_id}")

        print(f"✅ Utilisateur créé en local: {user_id}")

        # 5. Associer l'utilisateur à l'entreprise avec le nouveau système RBAC
        # Utiliser role_name au lieu de l'ancien enum role

        # Déterminer le rôle d'entreprise
        if hasattr(user_data, 'company_role') and user_data.company_role:
            company_role = user_data.company_role
        else:
            # Mapping par défaut des rôles système vers rôles d'entreprise
            company_role_mapping = {
                "ADMIN": "ADMIN",        # Admin reste Admin
                "SUPER_ADMIN": "ADMIN",  # Super admin devient Admin
                "CHEF_PROJET": "MANAGER", # Chef projet devient Manager
                "EMPLOYE": "USER",       # Employé devient User
                "CLIENT": "VIEWER"       # Client devient Viewer
            }
            company_role = company_role_mapping.get(user_role_value, "USER")

        print(f"🔄 Rôle entreprise assigné: '{company_role}'")

        user_company_query = text("""
            INSERT INTO user_companies (user_id, company_id, role_name, is_default, is_active, joined_at, created_at, updated_at)
            VALUES (:user_id, :company_id, :role_name, :is_default, :is_active, NOW(), NOW(), NOW())
        """)

        await db.execute(user_company_query, {
            "user_id": user_id,
            "company_id": user_data.company_id,
            "role_name": company_role,  # Utiliser role_name au lieu de role
            "is_default": True,
            "is_active": True
        })

        await db.commit()

        print(f"✅ Utilisateur associé à l'entreprise {user_data.company_id}")

        # Retourner la réponse avec l'ID local (pas l'UUID Supabase)
        response = AdminUserResponse(
            id=str(user_id),  # ID local pour compatibilité avec les autres endpoints
            email=user_data.email,
            first_name=user_data.first_name or "",
            last_name=user_data.last_name or "",
            role=user_role_value,  # Utiliser la valeur string
            is_active=True,
            is_verified=True,
            created_at="",  # Sera rempli par la base
            last_sign_in_at=None,
            company_role=company_role,  # Rôle métier BTP
            user_metadata={}
        )

        print(f"✅ Utilisateur créé avec ID local: {user_id} (Supabase: {supabase_user_id})")
        return response

    except HTTPException:
        # Re-lancer les HTTPException (erreurs déjà formatées)
        raise
    except Exception as e:
        await db.rollback()
        error_msg = str(e)
        print(f"❌ Erreur création utilisateur: {error_msg}")

        # Gestion des erreurs spécifiques
        if "duplicate key" in error_msg.lower() or "unique constraint" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Un utilisateur avec ces informations existe déjà dans la base de données"
            )
        elif "foreign key" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"L'entreprise avec l'ID {user_data.company_id} n'existe pas"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Erreur interne lors de la création de l'utilisateur: {error_msg}"
            )

@router.put("/{user_id}", response_model=AdminUserResponse)
async def update_user(
    user_id: str,
    user_data: AdminUserUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """
    Mettre à jour un utilisateur dans la base locale et Supabase
    """
    try:
        from app.models.user import User
        from app.models.company import UserCompany
        from sqlalchemy import select

        print(f"🔄 Modification utilisateur: {user_id}")

        # 1. Récupérer l'utilisateur local par ID ou UUID Supabase
        print(f"🔍 Recherche utilisateur avec ID: {user_id}")

        # Essayer d'abord par ID local (entier)
        local_user = None
        try:
            if user_id.isdigit():
                user_result = await db.execute(select(User).where(User.id == int(user_id)))
                local_user = user_result.scalar_one_or_none()
        except:
            pass

        # Si pas trouvé, essayer par UUID Supabase
        if not local_user:
            user_result = await db.execute(select(User).where(User.supabase_user_id == user_id))
            local_user = user_result.scalar_one_or_none()

        if not local_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"User with id {user_id} not found"
            )

        # 2. Mettre à jour la base locale
        if user_data.email:
            local_user.email = user_data.email
        if user_data.first_name:
            local_user.first_name = user_data.first_name
        if user_data.last_name:
            local_user.last_name = user_data.last_name
        if user_data.role:
            local_user.role = user_data.role
        if user_data.is_active is not None:
            local_user.is_active = user_data.is_active

        # 3. Mettre à jour Supabase si on a un supabase_user_id
        if local_user.supabase_user_id:
            try:
                supabase_service = SupabaseService()

                # Préparer les données de mise à jour pour Supabase
                supabase_update_data = {}
                user_metadata = {}

                if user_data.email:
                    supabase_update_data["email"] = user_data.email
                if user_data.first_name:
                    user_metadata["first_name"] = user_data.first_name
                if user_data.last_name:
                    user_metadata["last_name"] = user_data.last_name
                if user_data.role:
                    user_metadata["role"] = user_data.role

                if user_metadata:
                    supabase_update_data["user_metadata"] = user_metadata

                # Gérer le statut actif/inactif
                if user_data.is_active is not None:
                    if user_data.is_active:
                        supabase_update_data["ban_duration"] = "none"
                    else:
                        supabase_update_data["ban_duration"] = "876000h"  # Ban for 100 years

                await supabase_service.update_user(local_user.supabase_user_id, **supabase_update_data)
                print(f"✅ Utilisateur mis à jour dans Supabase")

            except Exception as supabase_error:
                print(f"⚠️ Erreur mise à jour Supabase (continuons avec local): {supabase_error}")

        # 4. Récupérer les infos de l'entreprise
        company_result = await db.execute(
            select(UserCompany).where(UserCompany.user_id == local_user.id)
        )
        user_company = company_result.scalar_one_or_none()

        await db.commit()

        print(f"✅ Utilisateur {user_id} mis à jour")

        return AdminUserResponse(
            id=str(local_user.id),
            email=local_user.email or "",
            first_name=local_user.first_name or "",
            last_name=local_user.last_name or "",
            role=local_user.role or "user",
            is_active=local_user.is_active,
            is_verified=local_user.is_verified,
            created_at=local_user.created_at.isoformat() if local_user.created_at else "",
            last_sign_in_at=None,
            company_role=user_company.role if user_company else "user",
            user_metadata={}
        )

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        error_msg = str(e)
        print(f"❌ Erreur modification utilisateur: {error_msg}")

        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Utilisateur avec l'ID {user_id} introuvable"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Erreur lors de la modification de l'utilisateur: {error_msg}"
            )

@router.delete("/{user_id}")
async def delete_user(
    user_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """
    Supprimer un utilisateur de la base locale et Supabase
    """
    try:
        from app.models.user import User
        from app.models.company import UserCompany
        from sqlalchemy import select

        print(f"🔄 Suppression utilisateur: {user_id}")

        # 1. Récupérer l'utilisateur local par ID ou UUID Supabase
        print(f"🔍 Recherche utilisateur à supprimer avec ID: {user_id}")

        # Essayer d'abord par ID local (entier)
        local_user = None
        try:
            if user_id.isdigit():
                user_result = await db.execute(select(User).where(User.id == int(user_id)))
                local_user = user_result.scalar_one_or_none()
        except:
            pass

        # Si pas trouvé, essayer par UUID Supabase
        if not local_user:
            user_result = await db.execute(select(User).where(User.supabase_user_id == user_id))
            local_user = user_result.scalar_one_or_none()

        if not local_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"User with id {user_id} not found"
            )

        # 2. Supprimer de Supabase si on a un supabase_user_id
        if local_user.supabase_user_id:
            try:
                supabase_service = SupabaseService()
                await supabase_service.delete_user(local_user.supabase_user_id)
                print(f"✅ Utilisateur supprimé de Supabase")
            except Exception as supabase_error:
                print(f"⚠️ Erreur suppression Supabase (continuons avec local): {supabase_error}")

        # 3. Supprimer les associations d'entreprise
        company_associations_result = await db.execute(
            select(UserCompany).where(UserCompany.user_id == local_user.id)
        )
        company_associations = company_associations_result.scalars().all()
        for association in company_associations:
            await db.delete(association)

        # 4. Supprimer l'utilisateur local
        await db.delete(local_user)
        await db.commit()

        print(f"✅ Utilisateur {user_id} supprimé")

        return {"success": True, "message": "User deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        error_msg = str(e)
        print(f"❌ Erreur suppression utilisateur: {error_msg}")

        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Utilisateur avec l'ID {user_id} introuvable"
            )
        elif "foreign key" in error_msg.lower() or "constraint" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Impossible de supprimer cet utilisateur car il est lié à d'autres données"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Erreur lors de la suppression de l'utilisateur: {error_msg}"
            )

@router.post("/{user_id}/toggle-status", response_model=AdminUserResponse)
async def toggle_user_status(
    user_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """
    Activer/désactiver un utilisateur (basculer le statut)
    """
    try:
        from app.models.user import User
        from app.models.company import UserCompany
        from sqlalchemy import select

        print(f"🔄 Basculement statut utilisateur: {user_id}")

        # 1. Récupérer l'utilisateur local par ID ou UUID Supabase
        print(f"🔍 Recherche utilisateur pour toggle statut avec ID: {user_id}")

        # Essayer d'abord par ID local (entier)
        local_user = None
        try:
            if user_id.isdigit():
                user_result = await db.execute(select(User).where(User.id == int(user_id)))
                local_user = user_result.scalar_one_or_none()
        except:
            pass

        # Si pas trouvé, essayer par UUID Supabase
        if not local_user:
            user_result = await db.execute(select(User).where(User.supabase_user_id == user_id))
            local_user = user_result.scalar_one_or_none()

        if not local_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"User with id {user_id} not found"
            )

        # 2. Basculer le statut
        new_status = not local_user.is_active
        local_user.is_active = new_status

        # 3. Mettre à jour Supabase si on a un supabase_user_id
        if local_user.supabase_user_id:
            try:
                supabase_service = SupabaseService()

                # Préparer les données de mise à jour pour le statut
                update_data = {}
                if new_status:
                    update_data["ban_duration"] = "none"
                else:
                    update_data["ban_duration"] = "876000h"  # Ban for 100 years

                await supabase_service.update_user(local_user.supabase_user_id, **update_data)
                print(f"✅ Statut mis à jour dans Supabase: {'Actif' if new_status else 'Inactif'}")

            except Exception as supabase_error:
                print(f"⚠️ Erreur mise à jour statut Supabase (continuons avec local): {supabase_error}")

        # 4. Récupérer les infos de l'entreprise
        company_result = await db.execute(
            select(UserCompany).where(UserCompany.user_id == local_user.id)
        )
        user_company = company_result.scalar_one_or_none()

        await db.commit()

        print(f"✅ Statut utilisateur {user_id} basculé vers: {'Actif' if new_status else 'Inactif'}")

        return AdminUserResponse(
            id=str(local_user.id),
            email=local_user.email or "",
            first_name=local_user.first_name or "",
            last_name=local_user.last_name or "",
            role=local_user.role or "user",
            is_active=local_user.is_active,
            is_verified=local_user.is_verified,
            created_at=local_user.created_at.isoformat() if local_user.created_at else "",
            last_sign_in_at=None,
            company_role=user_company.role if user_company else "user",
            user_metadata={}
        )

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        error_msg = str(e)
        print(f"❌ Erreur basculement statut utilisateur: {error_msg}")

        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Utilisateur avec l'ID {user_id} introuvable"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Erreur lors du changement de statut de l'utilisateur: {error_msg}"
            )

@router.post("/users/{user_id}/companies/{company_id}/role")
async def update_user_company_role(
    user_id: int,
    company_id: int,
    role_update: UserRoleUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_super_admin)
):
    """
    Mettre à jour le rôle d'un utilisateur dans une entreprise
    """
    try:
        from app.services.rbac_service import RBACService

        rbac_service = RBACService(db)
        success = await rbac_service.assign_user_role(user_id, company_id, role_update.role_name)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Utilisateur ou entreprise non trouvé"
            )

        return {
            "message": f"Rôle {role_update.role_name} assigné à l'utilisateur dans l'entreprise",
            "user_id": user_id,
            "company_id": company_id,
            "new_role": role_update.role_name
        }

    except Exception as e:
        print(f"❌ Erreur lors de la mise à jour du rôle: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la mise à jour du rôle: {str(e)}"
        )
