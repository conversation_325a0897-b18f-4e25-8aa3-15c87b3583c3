'use client'

import React, { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { DocumentType, TechnicalDocumentResponse } from '@/types/technical-document'
import DocumentManager from '@/components/technical-documents/DocumentManager'
import TechnicalDocumentEditor from '@/components/editor/TechnicalDocumentEditor'
import CompanySelector from '@/components/technical-documents/CompanySelector'
import ContextMenu, { useContextMenu } from '@/components/technical-documents/ContextMenu'
import { useTechnicalDocument, useCreateTechnicalDocument, useAutoSave } from '@/hooks/useTechnicalDocument'
import { useChatGPT } from '@/hooks/useChatGPT'

export default function DocumentsTechniquesPage() {
  const searchParams = useSearchParams()
  const [selectedDocument, setSelectedDocument] = useState<TechnicalDocumentResponse | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [newDocumentType, setNewDocumentType] = useState<DocumentType | null>(null)
  const [newDocumentName, setNewDocumentName] = useState('')

  // Récupérer le project_id depuis l'URL ou utiliser une valeur par défaut
  const selectedProjectId = parseInt(searchParams.get('project_id') || '7')

  // Hooks pour la gestion des documents
  const { document, loading, error, save, refresh } = useTechnicalDocument(selectedDocument?.id)
  const { createDocument, loading: creating } = useCreateTechnicalDocument()
  const { enhanceText, loading: enhancing } = useChatGPT()

  // Hook pour la sauvegarde automatique
  const { addPendingChange, isSaving, lastSaved, hasPendingChanges } = useAutoSave(
    selectedDocument,
    save,
    3000 // 3 secondes de délai
  )

  // Hook pour le menu contextuel
  const { contextMenu, showContextMenu, hideContextMenu } = useContextMenu()

  // Mettre à jour le document sélectionné quand il est chargé
  useEffect(() => {
    if (document) {
      setSelectedDocument(document)
    }
  }, [document])

  // Gérer la sélection d'un document
  const handleDocumentSelect = (doc: TechnicalDocumentResponse) => {
    setSelectedDocument(doc)
    setIsCreating(false)
  }

  // Gérer la création d'un nouveau document
  const handleDocumentCreate = (type: DocumentType) => {
    setNewDocumentType(type)
    setIsCreating(true)
    setSelectedDocument(null)
  }

  // Créer un nouveau document
  const handleCreateNewDocument = async (name: string, content: string = '') => {
    if (!newDocumentType) return

    try {
      const newDoc = await createDocument({
        name,
        type_document: newDocumentType,
        project_id: selectedProjectId,
        content,
        company_ids: []
      })

      setSelectedDocument(newDoc)
      setIsCreating(false)
      setNewDocumentType(null)
      setNewDocumentName('')
    } catch (err) {
      console.error('Erreur lors de la création:', err)
    }
  }

  // Gérer les modifications du contenu
  const handleContentChange = (content: string) => {
    if (selectedDocument) {
      addPendingChange({ content })
    }
  }

  // Gérer les modifications des entreprises
  const handleCompaniesChange = async (companyIds: number[]) => {
    if (selectedDocument) {
      try {
        await save({ company_ids: companyIds })
        await refresh()
      } catch (err) {
        console.error('Erreur lors de la mise à jour des entreprises:', err)
      }
    }
  }

  // Gérer l'amélioration de texte avec IA
  const handleTextEnhancement = async (promptType: string) => {
    if (!contextMenu.selectedText || !selectedDocument) return

    try {
      const result = await enhanceText({
        text: contextMenu.selectedText,
        prompt_type: promptType,
        document_type: selectedDocument.type_document!,
        context: `Document: ${selectedDocument.name}`
      })

      // Insérer le texte amélioré dans l'éditeur
      // Cette fonctionnalité nécessiterait une référence à l'éditeur TinyMCE
      console.log('Texte amélioré:', result.enhanced_text)
      
    } catch (err) {
      console.error('Erreur lors de l\'amélioration:', err)
    }
  }

  // Gérer la sélection de texte pour le menu contextuel
  const handleTextSelection = (selectedText: string) => {
    // Le menu contextuel sera affiché via un clic droit
    // Cette fonction prépare juste le texte sélectionné
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* En-tête */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Documents Techniques</h1>
          <p className="mt-2 text-gray-600">
            Gestion des documents CCTP et DPGF avec amélioration IA
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Panneau de gauche - Liste des documents */}
          <div className="lg:col-span-1">
            <DocumentManager
              projectId={selectedProjectId}
              onDocumentSelect={handleDocumentSelect}
              onDocumentCreate={handleDocumentCreate}
            />
          </div>

          {/* Panneau principal - Éditeur */}
          <div className="lg:col-span-2">
            {isCreating ? (
              // Mode création
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">
                  Nouveau document {newDocumentType}
                </h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nom du document
                    </label>
                    <input
                      type="text"
                      value={newDocumentName}
                      onChange={(e) => setNewDocumentName(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder={`Nom du ${newDocumentType}...`}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          if (newDocumentName.trim()) {
                            handleCreateNewDocument(newDocumentName.trim())
                          }
                        }
                      }}
                    />
                  </div>
                  
                  <div className="flex space-x-3">
                    <button
                      onClick={() => {
                        if (newDocumentName.trim()) {
                          handleCreateNewDocument(newDocumentName.trim())
                        }
                      }}
                      disabled={creating || !newDocumentName.trim()}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                    >
                      {creating ? 'Création...' : 'Créer'}
                    </button>
                    
                    <button
                      onClick={() => {
                        setIsCreating(false)
                        setNewDocumentType(null)
                        setNewDocumentName('')
                      }}
                      className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                    >
                      Annuler
                    </button>
                  </div>
                </div>
              </div>
            ) : selectedDocument ? (
              // Mode édition
              <div className="space-y-6">
                {/* Informations du document */}
                <div className="bg-white rounded-lg shadow p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h2 className="text-xl font-semibold">{selectedDocument.name}</h2>
                      <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          selectedDocument.type_document === DocumentType.CCTP
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {selectedDocument.type_document}
                        </span>
                        {selectedDocument.project && (
                          <span>Projet: {selectedDocument.project.name}</span>
                        )}
                      </div>
                    </div>
                    
                    {/* Indicateurs de sauvegarde */}
                    <div className="flex items-center space-x-2 text-sm">
                      {isSaving && (
                        <span className="text-blue-600 flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-1"></div>
                          Sauvegarde...
                        </span>
                      )}
                      {hasPendingChanges && !isSaving && (
                        <span className="text-orange-600">Modifications non sauvegardées</span>
                      )}
                      {lastSaved && !hasPendingChanges && !isSaving && (
                        <span className="text-green-600">
                          Sauvegardé à {lastSaved.toLocaleTimeString()}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Sélecteur d'entreprises */}
                  <CompanySelector
                    selectedCompanies={selectedDocument.companies || []}
                    onSelectionChange={(companies) => {
                      const companyIds = companies.map(c => c.id)
                      handleCompaniesChange(companyIds)
                    }}
                    loading={loading}
                    error={error}
                  />
                </div>

                {/* Éditeur */}
                <div className="bg-white rounded-lg shadow">
                  <TechnicalDocumentEditor
                    value={selectedDocument.content || ''}
                    onChange={handleContentChange}
                    documentType={selectedDocument.type_document!}
                    onTextSelection={handleTextSelection}
                    placeholder={`Commencez à rédiger votre ${selectedDocument.type_document}...`}
                  />
                </div>
              </div>
            ) : (
              // État vide
              <div className="bg-white rounded-lg shadow p-12 text-center">
                <div className="text-gray-400 mb-4">
                  <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Aucun document sélectionné
                </h3>
                <p className="text-gray-500">
                  Sélectionnez un document existant ou créez-en un nouveau pour commencer.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Menu contextuel pour l'amélioration IA */}
        <ContextMenu
          visible={contextMenu.visible}
          position={contextMenu.position}
          selectedText={contextMenu.selectedText}
          documentType={selectedDocument?.type_document || DocumentType.CCTP}
          onAction={handleTextEnhancement}
          onClose={hideContextMenu}
        />

        {/* Indicateur de chargement global */}
        {enhancing && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span>Amélioration du texte en cours...</span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
