# app/api/api_v1/endpoints/employees.py
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.models.employee import Employee, TimeEntry
from app.schemas.employee import Employee as EmployeeSchema, EmployeeCreate, EmployeeUpdate, TimeEntry as TimeEntrySchema, TimeEntryCreate

router = APIRouter()

@router.get("/", response_model=List[EmployeeSchema])
def read_employees(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    company_id: int = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve employees.
    """
    if company_id:
        deps.get_company_access(company_id, current_user, db)
        employees = db.query(Employee).filter(Employee.company_id == company_id).offset(skip).limit(limit).all()
    else:
        from app.models.company import UserCompany
        user_companies = db.query(UserCompany).filter(UserCompany.user_id == current_user.id).all()
        company_ids = [uc.company_id for uc in user_companies]
        employees = db.query(Employee).filter(Employee.company_id.in_(company_ids)).offset(skip).limit(limit).all()
    
    return employees

@router.post("/", response_model=EmployeeSchema)
def create_employee(
    *,
    db: Session = Depends(deps.get_db),
    employee_in: EmployeeCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new employee.
    """
    deps.get_company_access(employee_in.company_id, current_user, db)
    
    # Check if employee number already exists for this company
    existing = db.query(Employee).filter(
        Employee.company_id == employee_in.company_id,
        Employee.employee_number == employee_in.employee_number
    ).first()
    if existing:
        raise HTTPException(status_code=400, detail="Employee number already exists for this company")
    
    employee = Employee(**employee_in.dict())
    db.add(employee)
    db.commit()
    db.refresh(employee)
    return employee

@router.get("/{id}", response_model=EmployeeSchema)
def read_employee(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get employee by ID.
    """
    employee = db.query(Employee).filter(Employee.id == id).first()
    if not employee:
        raise HTTPException(status_code=404, detail="Employee not found")
    
    deps.get_company_access(employee.company_id, current_user, db)
    return employee

@router.put("/{id}", response_model=EmployeeSchema)
def update_employee(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    employee_in: EmployeeUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update an employee.
    """
    employee = db.query(Employee).filter(Employee.id == id).first()
    if not employee:
        raise HTTPException(status_code=404, detail="Employee not found")
    
    deps.get_company_access(employee.company_id, current_user, db)
    
    for field, value in employee_in.dict(exclude_unset=True).items():
        setattr(employee, field, value)
    
    db.add(employee)
    db.commit()
    db.refresh(employee)
    return employee

@router.post("/time-entries", response_model=TimeEntrySchema)
def create_time_entry(
    *,
    db: Session = Depends(deps.get_db),
    time_entry_in: TimeEntryCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new time entry.
    """
    employee = db.query(Employee).filter(Employee.id == time_entry_in.employee_id).first()
    if not employee:
        raise HTTPException(status_code=404, detail="Employee not found")
    
    deps.get_company_access(employee.company_id, current_user, db)
    
    time_entry = TimeEntry(**time_entry_in.dict(), user_id=current_user.id)
    db.add(time_entry)
    db.commit()
    db.refresh(time_entry)
    return time_entry

@router.get("/{id}/timesheet", response_model=List[TimeEntrySchema])
def get_employee_timesheet(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get employee timesheet.
    """
    employee = db.query(Employee).filter(Employee.id == id).first()
    if not employee:
        raise HTTPException(status_code=404, detail="Employee not found")
    
    deps.get_company_access(employee.company_id, current_user, db)
    
    time_entries = db.query(TimeEntry).filter(TimeEntry.employee_id == id).all()
    return time_entries