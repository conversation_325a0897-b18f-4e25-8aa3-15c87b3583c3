# app/api/api_v1/endpoints/companies.py
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.models.company import Company, UserCompany, CompanySettings
from app.schemas.company import Company as CompanySchema, CompanyCreate, CompanyUpdate, CompanySettings as CompanySettingsSchema

router = APIRouter()

@router.get("/", response_model=List[CompanySchema])
def read_companies(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve companies accessible by current user.
    """
    user_companies = db.query(UserCompany).filter(UserCompany.user_id == current_user.id).all()
    company_ids = [uc.company_id for uc in user_companies]
    companies = db.query(Company).filter(Company.id.in_(company_ids)).offset(skip).limit(limit).all()
    return companies

@router.post("/", response_model=CompanySchema)
def create_company(
    *,
    db: Session = Depends(deps.get_db),
    company_in: CompanyCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new company.
    """
    # Check if code already exists
    if db.query(Company).filter(Company.code == company_in.code).first():
        raise HTTPException(status_code=400, detail="Company code already exists")
    
    # Check company limit
    user_company_count = db.query(UserCompany).filter(UserCompany.user_id == current_user.id).count()
    if user_company_count >= 10:
        raise HTTPException(status_code=400, detail="Maximum number of companies reached (10)")
    
    company = Company(**company_in.dict())
    db.add(company)
    db.commit()
    db.refresh(company)
    
    # Associate user with company
    user_company = UserCompany(user_id=current_user.id, company_id=company.id, is_default=user_company_count == 0)
    db.add(user_company)
    
    # Create default settings
    settings = CompanySettings(company_id=company.id)
    db.add(settings)
    
    db.commit()
    return company

@router.get("/{id}", response_model=CompanySchema)
def read_company(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get company by ID.
    """
    company = deps.get_company_access(id, current_user, db)
    return company

@router.put("/{id}", response_model=CompanySchema)
def update_company(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    company_in: CompanyUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a company.
    """
    company = deps.get_company_access(id, current_user, db)
    
    for field, value in company_in.dict(exclude_unset=True).items():
        setattr(company, field, value)
    
    db.add(company)
    db.commit()
    db.refresh(company)
    return company

@router.get("/{id}/settings", response_model=CompanySettingsSchema)
def read_company_settings(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get company settings.
    """
    deps.get_company_access(id, current_user, db)
    settings = db.query(CompanySettings).filter(CompanySettings.company_id == id).first()
    if not settings:
        # Create default settings if none exist
        settings = CompanySettings(company_id=id)
        db.add(settings)
        db.commit()
        db.refresh(settings)
    return settings

@router.put("/{id}/settings", response_model=CompanySettingsSchema)
def update_company_settings(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    settings_in: CompanySettingsSchema,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update company settings.
    """
    deps.get_company_access(id, current_user, db)
    settings = db.query(CompanySettings).filter(CompanySettings.company_id == id).first()
    
    if not settings:
        settings = CompanySettings(company_id=id, **settings_in.dict(exclude={'id', 'company_id'}))
        db.add(settings)
    else:
        for field, value in settings_in.dict(exclude={'id', 'company_id'}, exclude_unset=True).items():
            setattr(settings, field, value)
        db.add(settings)
    
    db.commit()
    db.refresh(settings)
    return settings