'use client'

import { Modal } from '@/components/ui/Modal'
import { Input } from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'

interface EntrepriseFormData {
  nom_entreprise: string
  activite: string
  adresse: string
  code_postal: string
  ville: string
  pays: string
  telephone: string
  fax: string
  email: string
  siret: string
  tva_intracommunautaire: string
}

interface EntrepriseModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (e: React.FormEvent) => void
  formData: EntrepriseFormData
  setFormData: (data: EntrepriseFormData) => void
  isEdit?: boolean
  loading?: boolean
}

export default function EntrepriseModal({
  isOpen,
  onClose,
  onSubmit,
  formData,
  setFormData,
  isEdit = false,
  loading = false
}: EntrepriseModalProps) {
  
  const handleInputChange = (field: keyof EntrepriseFormData, value: string) => {
    setFormData({
      ...formData,
      [field]: value
    })
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="p-6">
        <h2 className="text-xl font-semibold mb-6">
          {isEdit ? 'Modifier l\'entreprise' : 'Nouvelle entreprise'}
        </h2>
        
        <form onSubmit={onSubmit} className="space-y-4">
          {/* Informations de base */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <Input
                label="Nom de l'entreprise *"
                value={formData.nom_entreprise}
                onChange={(e) => handleInputChange('nom_entreprise', e.target.value)}
                required
                placeholder="Nom de l'entreprise"
              />
            </div>
            
            <div className="md:col-span-2">
              <Input
                label="Activité"
                value={formData.activite}
                onChange={(e) => handleInputChange('activite', e.target.value)}
                placeholder="Secteur d'activité"
              />
            </div>
          </div>

          {/* Adresse */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 border-b pb-2">Adresse</h3>
            
            <Input
              label="Adresse"
              value={formData.adresse}
              onChange={(e) => handleInputChange('adresse', e.target.value)}
              placeholder="Adresse complète"
            />
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                label="Code postal"
                value={formData.code_postal}
                onChange={(e) => handleInputChange('code_postal', e.target.value)}
                placeholder="75000"
              />
              
              <Input
                label="Ville"
                value={formData.ville}
                onChange={(e) => handleInputChange('ville', e.target.value)}
                placeholder="Paris"
              />
              
              <Input
                label="Pays"
                value={formData.pays}
                onChange={(e) => handleInputChange('pays', e.target.value)}
                placeholder="France"
              />
            </div>
          </div>

          {/* Contact */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 border-b pb-2">Contact</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Téléphone"
                value={formData.telephone}
                onChange={(e) => handleInputChange('telephone', e.target.value)}
                placeholder="01 23 45 67 89"
              />
              
              <Input
                label="Fax"
                value={formData.fax}
                onChange={(e) => handleInputChange('fax', e.target.value)}
                placeholder="01 23 45 67 90"
              />
            </div>
            
            <Input
              label="Email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>

          {/* Informations légales */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 border-b pb-2">Informations légales</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="SIRET"
                value={formData.siret}
                onChange={(e) => handleInputChange('siret', e.target.value)}
                placeholder="12345678901234"
                maxLength={14}
              />
              
              <Input
                label="TVA intracommunautaire"
                value={formData.tva_intracommunautaire}
                onChange={(e) => handleInputChange('tva_intracommunautaire', e.target.value)}
                placeholder="FR12345678901"
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              disabled={loading || !formData.nom_entreprise.trim()}
            >
              {loading ? 'Enregistrement...' : (isEdit ? 'Modifier' : 'Créer')}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  )
}
