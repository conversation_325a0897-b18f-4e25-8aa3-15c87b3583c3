# app/models/entreprise_tiers.py
"""
Modèle pour les entreprises tierces (carnet d'adresses)
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base


class EntrepriseTiers(Base):
    """
    Modèle pour les entreprises tierces - carnet d'adresses
    """
    __tablename__ = "entreprises_tiers"

    id = Column(Integer, primary_key=True, index=True)
    
    # Informations de base
    nom_entreprise = Column(String(255), nullable=False, index=True)
    activite = Column(String(255), nullable=True)
    
    # Adresse
    adresse = Column(Text, nullable=True)
    code_postal = Column(String(10), nullable=True)
    ville = Column(String(100), nullable=True)
    pays = Column(String(100), nullable=True, default="France")
    
    # Contact
    telephone = Column(String(20), nullable=True)
    fax = Column(String(20), nullable=True)
    email = Column(String(255), nullable=True, index=True)
    
    # Informations légales
    siret = Column(String(14), nullable=True, unique=True, index=True)
    tva_intracommunautaire = Column(String(20), nullable=True)
    
    # Représentant légal
    representant_legal_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Relation avec l'entreprise propriétaire
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False, index=True)
    
    # Métadonnées
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Relations
    company = relationship("Company", back_populates="entreprises_tiers")
    representant_legal = relationship("User", foreign_keys=[representant_legal_id])
    created_by_user = relationship("User", foreign_keys=[created_by])

    def __repr__(self):
        return f"<EntrepriseTiers(id={self.id}, nom='{self.nom_entreprise}', siret='{self.siret}')>"
