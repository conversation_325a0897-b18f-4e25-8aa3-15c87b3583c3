'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  Building2,
  Home,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

const navigationItems = [
  {
    name: 'Tableau de bord',
    href: '/',
    icon: Home
  },
  {
    name: 'Entreprises',
    href: '/companies',
    icon: Building2
  }
]

interface SidebarProps {
  user?: {
    email: string
    name?: string
  }
}

export default function Sidebar({ user }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const pathname = usePathname()

  return (
    <>
      {/* Sidebar */}
      <div className={`
        fixed left-0 top-0 z-40 h-screen bg-white border-r border-gray-200 shadow-lg
        transition-all duration-300 ease-in-out
        ${isCollapsed ? 'w-16' : 'w-64'}
      `}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            {!isCollapsed && (
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Building2 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">ORBIS</h1>
                  <p className="text-xs text-teal-600 font-medium">ADMIN PORTAL</p>
                </div>
              </div>
            )}
            
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors"
              title={isCollapsed ? 'Étendre' : 'Réduire'}
            >
              {isCollapsed ? (
                <ChevronRight className="w-4 h-4 text-gray-600" />
              ) : (
                <ChevronLeft className="w-4 h-4 text-gray-600" />
              )}
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-3 py-4 space-y-1">
            {navigationItems.map((item) => {
              const isActive = item.href === '/'
                ? pathname === '/'
                : pathname.startsWith(item.href)
              const IconComponent = item.icon

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`sidebar-item ${isActive ? 'active' : ''}`}
                  title={isCollapsed ? item.name : undefined}
                >
                  <div className={`flex-shrink-0 ${isActive ? 'text-teal-600' : 'text-gray-500'}`}>
                    <IconComponent className="w-5 h-5" />
                  </div>
                  {!isCollapsed && (
                    <>
                      <span className="ml-3 truncate">{item.name}</span>
                      {item.badge && (
                        <span className="ml-auto badge-success">
                          {item.badge}
                        </span>
                      )}
                    </>
                  )}
                  {isActive && !isCollapsed && (
                    <span className="absolute right-3 w-1.5 h-1.5 rounded-full bg-teal-600"></span>
                  )}
                </Link>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Spacer for fixed sidebar */}
      <div className={`${isCollapsed ? 'w-16' : 'w-64'} flex-shrink-0 transition-all duration-300`} />
    </>
  )
}
