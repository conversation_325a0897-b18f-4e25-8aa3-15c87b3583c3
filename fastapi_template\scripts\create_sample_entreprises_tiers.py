#!/usr/bin/env python3
"""
Script pour créer des entreprises tiers de test
"""

import asyncio
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import engine
from app.models.entreprise_tiers import EntrepriseTiers
from app.models.company import UserCompany
from app.models.user import User
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

async def create_sample_entreprises_tiers():
    """Créer des entreprises tiers de test"""
    
    async with AsyncSession(engine) as db:
        try:
            # Récupérer l'utilisateur test
            user_result = await db.execute(
                select(User).where(User.email == "<EMAIL>")
            )
            user = user_result.scalar_one_or_none()
            
            if not user:
                print("❌ Utilisateur <EMAIL> non trouvé")
                return
            
            # Récupérer l'entreprise de l'utilisateur
            user_company_result = await db.execute(
                select(UserCompany).where(
                    and_(
                        UserCompany.user_id == user.id,
                        UserCompany.is_active == True
                    )
                )
            )
            user_company = user_company_result.scalar_one_or_none()
            
            if not user_company:
                print("❌ Utilisateur <EMAIL> non associé à une entreprise")
                return
            
            print(f"✅ Utilisateur trouvé: {user.email} (Company ID: {user_company.company_id})")
            
            # Vérifier si des entreprises tiers existent déjà
            existing_result = await db.execute(
                select(EntrepriseTiers).where(
                    and_(
                        EntrepriseTiers.company_id == user_company.company_id,
                        EntrepriseTiers.is_active == True
                    )
                )
            )
            existing_count = len(existing_result.scalars().all())
            
            if existing_count > 0:
                print(f"ℹ️  {existing_count} entreprise(s) tiers déjà existante(s)")
                return
            
            # Créer des entreprises tiers de test
            entreprises_data = [
                {
                    "nom_entreprise": "Électricité Moderne SARL",
                    "activite": "Électricité générale",
                    "adresse": "15 Avenue des Électriciens",
                    "code_postal": "69001",
                    "ville": "Lyon",
                    "pays": "France",
                    "telephone": "04 78 12 34 56",
                    "email": "<EMAIL>",
                    "siret": "12345678901234",
                    "tva_intracommunautaire": "FR12345678901",
                    "company_id": user_company.company_id,
                    "created_by": user.id
                },
                {
                    "nom_entreprise": "Plomberie Expert",
                    "activite": "Plomberie et chauffage",
                    "adresse": "8 Rue des Plombiers",
                    "code_postal": "69002",
                    "ville": "Lyon",
                    "pays": "France",
                    "telephone": "04 78 98 76 54",
                    "email": "<EMAIL>",
                    "siret": "98765432109876",
                    "tva_intracommunautaire": "FR98765432109",
                    "company_id": user_company.company_id,
                    "created_by": user.id
                },
                {
                    "nom_entreprise": "Menuiserie Artisanale",
                    "activite": "Menuiserie bois et PVC",
                    "adresse": "22 Boulevard du Bois",
                    "code_postal": "69003",
                    "ville": "Lyon",
                    "pays": "France",
                    "telephone": "04 78 55 44 33",
                    "email": "<EMAIL>",
                    "siret": "11223344556677",
                    "tva_intracommunautaire": "FR11223344556",
                    "company_id": user_company.company_id,
                    "created_by": user.id
                },
                {
                    "nom_entreprise": "Peinture Déco Plus",
                    "activite": "Peinture et décoration",
                    "adresse": "5 Place des Artistes",
                    "code_postal": "69004",
                    "ville": "Lyon",
                    "pays": "France",
                    "telephone": "04 78 11 22 33",
                    "email": "<EMAIL>",
                    "siret": "55667788990011",
                    "company_id": user_company.company_id,
                    "created_by": user.id
                },
                {
                    "nom_entreprise": "Maçonnerie du Rhône",
                    "activite": "Maçonnerie générale",
                    "adresse": "30 Chemin des Maçons",
                    "code_postal": "69005",
                    "ville": "Lyon",
                    "pays": "France",
                    "telephone": "04 78 77 88 99",
                    "email": "<EMAIL>",
                    "siret": "33445566778899",
                    "tva_intracommunautaire": "FR33445566778",
                    "company_id": user_company.company_id,
                    "created_by": user.id
                }
            ]
            
            created_count = 0
            for entreprise_data in entreprises_data:
                entreprise = EntrepriseTiers(**entreprise_data)
                db.add(entreprise)
                created_count += 1
                print(f"➕ Création: {entreprise_data['nom_entreprise']}")
            
            await db.commit()
            print(f"✅ {created_count} entreprises tiers créées avec succès!")
            
        except Exception as e:
            print(f"❌ Erreur lors de la création des entreprises tiers: {str(e)}")
            await db.rollback()
            raise

if __name__ == "__main__":
    print("🏢 Création d'entreprises tiers de test...")
    asyncio.run(create_sample_entreprises_tiers())
    print("✅ Script terminé!")
