'use client'

import React from 'react'
import { DocumentType } from '@/types/technical-document'
import { PlusIcon } from '@heroicons/react/24/outline'

interface DocumentCreationButtonsProps {
  onDocumentCreate: (type: DocumentType) => void
}

export default function DocumentCreationButtons({
  onDocumentCreate
}: DocumentCreationButtonsProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Créer un document</h2>
      <div className="flex space-x-4">
        <button
          onClick={() => onDocumentCreate(DocumentType.CCTP)}
          className="inline-flex items-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          CCTP
        </button>
        <button
          onClick={() => onDocumentCreate(DocumentType.DPGF)}
          className="inline-flex items-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          DPGF
        </button>
      </div>
    </div>
  )
}
