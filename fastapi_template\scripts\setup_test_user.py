#!/usr/bin/env python3
"""
Script pour configurer l'utilisateur <NAME_EMAIL>
"""

import asyncio
import sys
import os
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import text, select
from app.core.database import engine
from app.models.user import User, UserRole
from app.models.company import Company, UserCompany
from app.models.project import Project, ProjectStatus, ProjectNature, ProjectCompany

async def setup_test_user():
    """Configurer l'utilisateur de test et ses données"""
    
    try:
        async with engine.begin() as conn:
            print("🔄 Configuration de l'utilisateur <EMAIL>...")
            
            # 1. Vérifier/créer l'utilisateur <EMAIL>
            user_check = await conn.execute(
                text("SELECT id, email, first_name, last_name FROM users WHERE email = '<EMAIL>'")
            )
            user = user_check.fetchone()
            
            if not user:
                print("   Création de l'utilisateur <EMAIL>...")
                user_insert = await conn.execute(
                    text("""
                    INSERT INTO users (email, first_name, last_name, role, is_active, created_at)
                    VALUES ('<EMAIL>', 'Test', 'ORBIS', 'ADMIN', true, CURRENT_TIMESTAMP)
                    RETURNING id, email, first_name, last_name
                    """)
                )
                user = user_insert.fetchone()
                print(f"   ✅ Utilisateur créé: {user.first_name} {user.last_name} ({user.email})")
            else:
                print(f"   ✅ Utilisateur existant: {user.first_name} {user.last_name} ({user.email})")
            
            # 2. Vérifier/créer l'entreprise ORBIS
            company_check = await conn.execute(
                text("SELECT id, name, code FROM companies WHERE code = 'ORBIS' OR name ILIKE '%ORBIS%'")
            )
            company = company_check.fetchone()
            
            if not company:
                print("   Création de l'entreprise ORBIS...")
                company_insert = await conn.execute(
                    text("""
                    INSERT INTO companies (name, code, description, is_active, created_at)
                    VALUES ('ORBIS Construction', 'ORBIS', 'Entreprise de test pour les documents techniques', true, CURRENT_TIMESTAMP)
                    RETURNING id, name, code
                    """)
                )
                company = company_insert.fetchone()
                print(f"   ✅ Entreprise créée: {company.name} ({company.code})")
            else:
                print(f"   ✅ Entreprise existante: {company.name} ({company.code})")
            
            # 3. Associer l'utilisateur à l'entreprise
            user_company_check = await conn.execute(
                text("SELECT id FROM user_companies WHERE user_id = :user_id AND company_id = :company_id"),
                {"user_id": user.id, "company_id": company.id}
            )
            user_company = user_company_check.fetchone()
            
            if not user_company:
                print("   Association utilisateur-entreprise...")
                await conn.execute(
                    text("""
                    INSERT INTO user_companies (user_id, company_id, role_name, is_default, is_active, joined_at, created_at)
                    VALUES (:user_id, :company_id, 'ADMIN', true, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    """),
                    {"user_id": user.id, "company_id": company.id}
                )
                print("   ✅ Association créée")
            else:
                print("   ✅ Association existante")
            
            # 4. Créer des projets de test
            projects_data = [
                {
                    "name": "Résidence Les Jardins",
                    "code": "RLJ2024",
                    "description": "Construction de 24 logements collectifs",
                    "nature": "AFFAIRE",
                    "status": "EN_COURS"
                },
                {
                    "name": "Rénovation Mairie",
                    "code": "RMA2024", 
                    "description": "Rénovation énergétique de la mairie",
                    "nature": "AO",
                    "status": "EN_COURS"
                },
                {
                    "name": "Extension École",
                    "code": "EEC2024",
                    "description": "Extension de l'école primaire",
                    "nature": "DEVIS",
                    "status": "EN_ATTENTE"
                }
            ]
            
            print("   Création des projets de test...")
            created_projects = []
            
            for project_data in projects_data:
                # Vérifier si le projet existe déjà
                project_check = await conn.execute(
                    text("SELECT id FROM projects WHERE code = :code"),
                    {"code": project_data["code"]}
                )
                existing_project = project_check.fetchone()
                
                if not existing_project:
                    project_insert = await conn.execute(
                        text("""
                        INSERT INTO projects (name, code, description, nature, status, created_by, created_at)
                        VALUES (:name, :code, :description, :nature, :status, :user_id, CURRENT_TIMESTAMP)
                        RETURNING id, name, code
                        """),
                        {
                            **project_data,
                            "user_id": user.id
                        }
                    )
                    project = project_insert.fetchone()
                    created_projects.append(project)
                    print(f"     ✅ Projet créé: {project.name} ({project.code})")
                    
                    # Associer le projet à l'entreprise
                    await conn.execute(
                        text("""
                        INSERT INTO project_companies (project_id, company_id, role, is_active, created_at)
                        VALUES (:project_id, :company_id, 'OWNER', true, CURRENT_TIMESTAMP)
                        ON CONFLICT (project_id, company_id) DO NOTHING
                        """),
                        {"project_id": project.id, "company_id": company.id}
                    )
                else:
                    print(f"     ✅ Projet existant: {project_data['name']} ({project_data['code']})")
            
            # 5. Créer des entreprises tierces pour les tests
            tiers_companies = [
                {"name": "BTP Solutions", "code": "BTPS", "description": "Entreprise de gros œuvre"},
                {"name": "Électricité Moderne", "code": "ELMO", "description": "Entreprise d'électricité"},
                {"name": "Plomberie Expert", "code": "PLEX", "description": "Entreprise de plomberie"}
            ]
            
            print("   Création des entreprises tierces...")
            for tiers_data in tiers_companies:
                tiers_check = await conn.execute(
                    text("SELECT id FROM companies WHERE code = :code"),
                    {"code": tiers_data["code"]}
                )
                existing_tiers = tiers_check.fetchone()
                
                if not existing_tiers:
                    await conn.execute(
                        text("""
                        INSERT INTO companies (name, code, description, is_active, created_at)
                        VALUES (:name, :code, :description, true, CURRENT_TIMESTAMP)
                        """),
                        tiers_data
                    )
                    print(f"     ✅ Entreprise tierce créée: {tiers_data['name']} ({tiers_data['code']})")
                else:
                    print(f"     ✅ Entreprise tierce existante: {tiers_data['name']} ({tiers_data['code']})")
            
            print("\n✅ Configuration terminée!")
            print("\n📋 Résumé:")
            print(f"   👤 Utilisateur: <EMAIL>")
            print(f"   🏢 Entreprise: {company.name} ({company.code})")
            print(f"   📁 Projets: {len(projects_data)} projets de test")
            print(f"   🤝 Entreprises tierces: {len(tiers_companies)} entreprises")
            
            print("\n🚀 Prêt pour les tests!")
            print("   1. Connectez-<NAME_EMAIL>")
            print("   2. Accédez aux documents techniques via le dashboard")
            print("   3. Créez des documents CCTP/DPGF")
            print("   4. Testez l'amélioration IA (nécessite OPENAI_API_KEY)")
            
    except Exception as e:
        print(f"❌ Erreur lors de la configuration: {e}")
        raise
    finally:
        await engine.dispose()

async def verify_setup():
    """Vérifier que la configuration est correcte"""
    
    try:
        async with engine.begin() as conn:
            print("\n🔍 Vérification de la configuration...")
            
            # Vérifier l'utilisateur
            user_check = await conn.execute(
                text("""
                SELECT u.id, u.email, u.first_name, u.last_name, u.role,
                       uc.role_name, c.name as company_name, c.code as company_code
                FROM users u
                LEFT JOIN user_companies uc ON u.id = uc.user_id AND uc.is_active = true
                LEFT JOIN companies c ON uc.company_id = c.id
                WHERE u.email = '<EMAIL>'
                """)
            )
            user_data = user_check.fetchone()
            
            if user_data:
                print(f"   ✅ Utilisateur: {user_data.first_name} {user_data.last_name}")
                print(f"      Email: {user_data.email}")
                print(f"      Rôle système: {user_data.role}")
                print(f"      Entreprise: {user_data.company_name} ({user_data.company_code})")
                print(f"      Rôle entreprise: {user_data.role_name}")
            else:
                print("   ❌ Utilisateur <EMAIL> non trouvé")
                return False
            
            # Vérifier les projets
            projects_check = await conn.execute(
                text("""
                SELECT p.id, p.name, p.code, p.nature, p.status
                FROM projects p
                JOIN project_companies pc ON p.id = pc.project_id
                JOIN companies c ON pc.company_id = c.id
                WHERE c.code = 'ORBIS' AND pc.is_active = true
                """)
            )
            projects = projects_check.fetchall()
            
            print(f"   ✅ Projets disponibles: {len(projects)}")
            for project in projects:
                print(f"      - {project.name} ({project.code}) - {project.nature} - {project.status}")
            
            # Vérifier les entreprises
            companies_check = await conn.execute(
                text("SELECT COUNT(*) as count FROM companies WHERE is_active = true")
            )
            companies_count = companies_check.scalar()
            print(f"   ✅ Entreprises disponibles: {companies_count}")
            
            return True
            
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False
    finally:
        await engine.dispose()

async def main():
    """Fonction principale"""
    print("🔧 Configuration de l'environnement de test")
    print("=" * 50)
    
    try:
        await setup_test_user()
        success = await verify_setup()
        
        if success:
            print("\n🎉 Configuration réussie!")
            print("\nPour tester le module:")
            print("1. Démarrez le backend: python -m uvicorn app.main:app --host 0.0.0.0 --port 8000")
            print("2. Démarrez le frontend: npm run dev")
            print("3. Connectez-<NAME_EMAIL>")
            print("4. Accédez à http://localhost:3000/documents-techniques")
        else:
            print("\n⚠️  Configuration incomplète")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Échec de la configuration: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
