# app/schemas/user.py
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, List
from datetime import datetime
from app.models.user import UserRole

class UserBase(BaseModel):
    email: Optional[EmailStr] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    full_name: Optional[str] = None
    role: Optional[UserRole] = UserRole.EMPLOYE
    is_active: Optional[bool] = True
    phone: Optional[str] = None
    avatar_url: Optional[str] = None
    notes: Optional[str] = None

class UserCreate(BaseModel):
    email: EmailStr
    password: str = Field(..., min_length=8, description="Password must be at least 8 characters long")
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    full_name: Optional[str] = None
    role: Optional[UserRole] = UserRole.EMPLOYE
    phone: Optional[str] = None
    is_superuser: Optional[bool] = False
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v
    
    @validator('full_name', always=True)
    def generate_full_name(cls, v, values):
        if not v and 'first_name' in values and 'last_name' in values:
            return f"{values['first_name']} {values['last_name']}"
        return v

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    full_name: Optional[str] = None
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None
    phone: Optional[str] = None
    avatar_url: Optional[str] = None
    notes: Optional[str] = None
    password: Optional[str] = None
    
    @validator('password')
    def validate_password(cls, v):
        if v and len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v

class UserInDBBase(UserBase):
    id: Optional[int] = None
    is_verified: Optional[bool] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    failed_login_attempts: Optional[int] = None
    locked_until: Optional[datetime] = None

    class Config:
        from_attributes = True

class User(UserInDBBase):
    """User schema for API responses"""
    display_name: Optional[str] = None
    
    @validator('display_name', always=True)
    def set_display_name(cls, v, values):
        if not v:
            full_name = values.get('full_name')
            first_name = values.get('first_name')
            last_name = values.get('last_name')
            email = values.get('email')
            
            if full_name:
                return full_name
            elif first_name and last_name:
                return f"{first_name} {last_name}"
            else:
                return email
        return v

class UserInDB(UserInDBBase):
    hashed_password: str

class UserResponse(User):
    """Extended user response with permissions"""
    permissions: Optional[List[str]] = []
    can_manage_users: Optional[bool] = False
    can_manage_projects: Optional[bool] = False

class UserWithCompanies(User):
    """Utilisateur avec ses entreprises et rôles"""
    companies: List[dict] = Field(default_factory=list, description="Entreprises et rôles de l'utilisateur")
    default_company_id: Optional[int] = None
    total_companies: int = 0

class UserCompanyRole(BaseModel):
    """Rôle d'un utilisateur dans une entreprise"""
    user_id: int
    company_id: int
    company_name: str
    role_name: str
    permissions: List[str] = Field(default_factory=list)
    is_default: bool = False
    is_active: bool = True

class UserPermissionSummary(BaseModel):
    """Résumé des permissions d'un utilisateur"""
    user_id: int
    email: str
    system_role: UserRole
    companies: List[UserCompanyRole] = Field(default_factory=list)
    total_permissions: int = 0
    
class LoginRequest(BaseModel):
    email: EmailStr
    password: str
    remember_me: Optional[bool] = False

class Token(BaseModel):
    access_token: str
    refresh_token: Optional[str] = None
    token_type: str = "bearer"
    expires_in: Optional[int] = None
    user: Optional[User] = None

class RefreshTokenRequest(BaseModel):
    refresh_token: str

class TokenPayload(BaseModel):
    sub: Optional[int] = None
    exp: Optional[int] = None
    iat: Optional[int] = None
    jti: Optional[str] = None  # JWT ID for token blacklisting
    permissions: Optional[List[str]] = []

class ForgotPasswordRequest(BaseModel):
    email: EmailStr

class ResetPasswordRequest(BaseModel):
    token: str
    new_password: str = Field(..., min_length=8)
    
    @validator('new_password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v

class ChangePasswordRequest(BaseModel):
    current_password: str
    new_password: str = Field(..., min_length=8)
    
    @validator('new_password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v